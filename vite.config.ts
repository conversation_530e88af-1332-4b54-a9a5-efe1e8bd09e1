import replace from '@rollup/plugin-replace';
import react from '@vitejs/plugin-react';
import path from 'path';
import { defineConfig } from 'vite';
import monacoEditorPlugin from 'vite-plugin-monaco-editor';
import svgr from 'vite-plugin-svgr';
import fis3 from './scripts/fis3plugin';
import markdown from './scripts/markdownPlugin';
import mockApi from './scripts/mockApiPlugin';
import transformMobileHtml from './scripts/transformMobileHtml';

//@ts-ignore
import i18nPlugin from 'plugin-react-i18n';
import i18nConfig from './i18nConfig';

var I18N = true;

// https://vitejs.dev/config/
export default defineConfig({
  define: {
    'process.env.POS_ICONFONT_PATH': JSON.stringify('./pos-assets/iconfont.js'),
  },
  plugins: [
    I18N && i18nPlugin(i18nConfig), // 此插件的作用是将文件中的中文替换成_i18n(xxx)形式

    fis3(),
    markdown(),
    mockApi(),
    transformMobileHtml(),

    react({
      babel: {
        parserOpts: {
          plugins: ['decorators-legacy', 'classProperties'],
        },
      },
    }),
    svgr({
      exportAsDefault: true,
      svgrOptions: {
        svgProps: {
          className: 'icon',
        },
        prettier: false,
        dimensions: false,
      },
      exclude: ['node_modules/pos-components/**'],
    }),
    monacoEditorPlugin({}),
    replace({
      __editor_i18n: !!I18N,
      preventAssignment: true,
    }),
    // importToCDN({
    //   enableInDevMode: true,
    //   modules: [
    //     {
    //       name: 'amis-widget',
    //       var: 'amisWidget',
    //       path: '/packages/amis-editor/public/js/amis-widget.js',
    //     },
    //   ],
    // }),
  ].filter(n => n),
  optimizeDeps: {
    include: ['amis-formula/lib/doc'],
    exclude: ['amis-core', 'amis-formula', 'amis', 'amis-ui'],
    esbuildOptions: {
      target: 'esnext',
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        silenceDeprecations: [
          'legacy-js-api',
          'import',
          'global-builtin',
          'color-functions',
          'mixed-decls',
          'slash-div',
        ],
      },
    },
  },
  server: {
    host: '0.0.0.0',
    port: 8888,
    proxy: {
      '/cdp': {
        target: 'http://*************:9000',
        changeOrigin: true,
        cookiePathRewrite: {
          '*': '/',
        },
      },
      // '/easycode': {
      //   target: 'http://*************:9000/',
      //   changeOrigin: true,
      //   rewrite: (path) => path.replace(/^\/easycode/, '/cdp/easycode')
      // },
      '/packages/easycode': {
        // 接口采用请求上一级路径下的/easycode的方式 所以本地需要加这个packages转发
        target: 'http://*************:9000/',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/packages/, '/cdp'),
      },
      '/odh-service': {
        target: 'http://*************:9000',
        changeOrigin: true,
      },
      '/custc': {
        target: 'http://************:9876',
        changeOrigin: true,
      },
      '/bcare': {
        target: 'http://*************/bcsc-service/',
        changeOrigin: true,
      },
    },
  },
  resolve: {
    alias: [
      {
        find: 'moment/locale',
        replacement: 'moment/dist/locale',
      },
      {
        find: 'amis-formula/lib',
        replacement: path.resolve(__dirname, './packages/amis-formula/src'),
      },
      {
        find: 'amis-formula',
        replacement: path.resolve(__dirname, './packages/amis-formula/src'),
      },
      {
        find: 'amis-ui/lib',
        replacement: path.resolve(__dirname, './packages/amis-ui/src'),
      },
      {
        find: 'amis-ui',
        replacement: path.resolve(__dirname, './packages/amis-ui/src'),
      },
      {
        find: 'amis-core/lib',
        replacement: path.resolve(__dirname, './packages/amis-core/src'),
      },
      {
        find: 'amis-core/esm',
        replacement: path.resolve(__dirname, './packages/amis-core/src'),
      },
      {
        find: 'amis-core',
        replacement: path.resolve(__dirname, './packages/amis-core/src'),
      },
      {
        find: 'amis/lib',
        replacement: path.resolve(__dirname, './packages/amis/src'),
      },
      {
        find: 'amis/schema.json',
        replacement: path.resolve(__dirname, './packages/amis/schema.json'),
      },
      {
        find: 'amis/esm',
        replacement: path.resolve(__dirname, './packages/amis/src'),
      },
      {
        find: 'amis',
        replacement: path.resolve(__dirname, './packages/amis/src'),
      },
      {
        find: 'amis-editor',
        replacement: path.resolve(__dirname, './packages/amis-editor/src'),
      },
      {
        find: 'amis-editor-core',
        replacement: path.resolve(__dirname, './packages/amis-editor-core/src'),
      },
      {
        find: 'office-viewer',
        replacement: path.resolve(__dirname, './packages/office-viewer/src'),
      },
      {
        find: 'amis-theme-editor-helper',
        replacement: path.resolve(
          __dirname,
          './packages/amis-theme-editor-helper/src',
        ),
      },
      /**
       * pos-components 本地开发使用
       * 通过别名引入本地 pad-pos 的 components，便于开发者开发
       * 请核对路径是否正确指向本地 pad-pos 项目路径
       */
      // {
      //   find: 'local-pad-pos-components',
      //   replacement: path.resolve(
      //     __dirname,
      //     '../polamis-pos/src/components/indexNew.ts'
      //   )
      // },
      // {
      //   find: '@',
      //   replacement: path.resolve(__dirname, '../polamis-pos/src')
      // },
    ],
  },
  build: {
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index-editor.html'),
      },
      output: {
        manualChunks(id) {
          if (
            id.includes('amis/src/renderers') &&
            !id.includes('amis/src/renderers/Business')
          ) {
            return 'renderers';
          }
        },
      },
    },
  },
});
