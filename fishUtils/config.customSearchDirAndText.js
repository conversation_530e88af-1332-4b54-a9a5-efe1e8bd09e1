/**
 * 文件搜索配置
 * 用于配置find-files-with-text.js脚本的搜索参数
 */

/* eslint-disable no-undef */

module.exports = {
    /**
     * 搜索目录 - 支持单个路径或路径数组
     */
    searchDir: ['/Users/<USER>/Documents/my-work-projects/coc92-core/COC/web/fish-web/crm/crm-portal/src/main/webapp/crm/modules'],

    /**
     * 搜索文本 - 支持单个文本或文本数组
     */
    searchText: ['crm/modules/fix/InstallInfo/views/component/AddContactManComponent', 'crm/modules/pos/orderview/views/OrderView'],

    /**
     * 文件扩展名过滤 - 只搜索指定扩展名的文件
     */
    fileExtensions: ['.js'],

    /**
     * 路径分割数组 - 用于截取文件路径的分割点
     * 生成的 files_array.js 中的路径将从这些分割点按顺序开始截取，存在则停止
     */
    splitArray: ['crm/modules'],

    /**
     * 自定义项目路径数组 - 用于在这些项目中搜索MappingDefine文件（开发者自行替换）
     * 脚本会在这些项目中查找MappingDefine*.js文件，检查是否包含files_array中的路径
     */
    customProjects: [
        '/Users/<USER>/Documents/my-work-projects/coc-all-projects/coc92-bol',
        '/Users/<USER>/Documents/my-work-projects/coc-all-projects/coc92-libyana',
        '/Users/<USER>/Documents/my-work-projects/coc-all-projects/coc92-smartfren',
        '/Users/<USER>/Documents/my-work-projects/coc-all-projects/coc92-time',
        '/Users/<USER>/Documents/my-work-projects/coc-all-projects/coc92-tm',
        '/Users/<USER>/Documents/my-work-projects/coc-all-projects/coc92-tmb2b',
        '/Users/<USER>/Documents/my-work-projects/coc-all-projects/coc92-voo'
    ]
};
