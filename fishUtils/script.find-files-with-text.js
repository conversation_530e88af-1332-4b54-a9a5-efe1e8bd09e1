/**
 * 查找目录下包含指定文本的所有文件
 * 生成包含文件路径数组的JavaScript文件
 */

/* eslint-disable no-undef, @typescript-eslint/no-require-imports, @typescript-eslint/no-unused-vars, no-useless-escape */

const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');

/**
 * 从config.customSearchDirAndText.js读取搜索配置
 * @returns {Object} - 搜索配置对象
 */
function loadSearchConfig() {
    try {
        delete require.cache[require.resolve('./config.customSearchDirAndText.js')];
        const config = require('./config.customSearchDirAndText.js');

        return {
            searchDir: Array.isArray(config.searchDir) ? config.searchDir : [config.searchDir],
            searchText: Array.isArray(config.searchText) ? config.searchText : [config.searchText],
            fileExtensions: config.fileExtensions || ['.js', '.json', '.md', '.txt'],
            splitArray: config.splitArray || [],
            customProjects: config.customProjects || []
        };
    } catch (error) {
        console.error('读取config.customSearchDirAndText.js失败:', error.message);
        return null;
    }
}

/**
 * 异步递归搜索目录下包含指定文本的所有文件
 * @param {string} dir - 搜索目录
 * @param {string} singleSearchText - 要搜索的单个文本
 * @param {Array} fileExtensions - 文件扩展名过滤
 * @returns {Array} - 包含指定文本的文件路径数组
 */
async function findFilesWithSingleText(dir, singleSearchText, fileExtensions) {
    const results = [];

    async function searchInDirectory(currentDir) {
        try {
            const items = await fs.readdir(currentDir);

            await Promise.all(
                items.map(async item => {
                    const fullPath = path.join(currentDir, item);

                    try {
                        const stat = await fs.stat(fullPath);

                        if (stat.isDirectory()) {
                            await searchInDirectory(fullPath);
                        } else if (stat.isFile()) {
                            const ext = path.extname(item).toLowerCase();
                            if (fileExtensions.length === 0 || fileExtensions.includes(ext)) {
                                try {
                                    const content = await fs.readFile(fullPath, 'utf8');
                                    // 检查是否包含当前搜索文本
                                    const hasMatch =
                                        content.includes(singleSearchText) ||
                                        content.includes(singleSearchText.replace(/\//g, '\\/')) ||
                                        content.includes(singleSearchText.replace(/\//g, '\\\\')) ||
                                        new RegExp(singleSearchText.replace(/[\/\\]/g, '[/\\\\]')).test(content);

                                    if (hasMatch) {
                                        results.push(fullPath);
                                        console.log(`✅ 找到匹配文件 [${singleSearchText}]: ${fullPath}`);
                                    }
                                } catch (error) {
                                    console.warn(`⚠️  无法读取文件: ${fullPath} - ${error.message}`);
                                }
                            }
                        }
                    } catch (error) {
                        console.warn(`⚠️  无法访问: ${fullPath} - ${error.message}`);
                    }
                })
            );
        } catch (error) {
            console.error(`❌ 读取目录失败: ${currentDir} - ${error.message}`);
        }
    }

    await searchInDirectory(dir);
    return results;
}

/**
 * 生成合并的搜索结果文件
 */
function generateCombinedResultsFile(searchResults, searchText, searchDir, splitArray, allMappingResults) {
    // 计算总文件数
    const totalFiles = Object.values(searchResults).reduce((total, dirResults) => {
        return total + Object.values(dirResults).reduce((dirTotal, textFiles) => dirTotal + textFiles.length, 0);
    }, 0);

    const content = `/**
 * 合并的文件搜索结果
 * 搜索目录: ${searchDir.join(', ')}
 * 搜索文本: ${searchText.join(', ')}
 * 生成时间: ${new Date().toLocaleString()}
 * 找到文件数量: ${totalFiles}
 * 路径处理: 基于splitArray截取并去除后缀
 * MappingDefine匹配数量: ${allMappingResults.length}
 */

/* eslint-disable no-undef */

const searchResults = {
    metadata: {
        searchDir: ${JSON.stringify(searchDir)},
        searchText: ${JSON.stringify(searchText)},
        generateTime: '${new Date().toISOString()}',
        totalFiles: ${totalFiles},
        processedFiles: ${Object.values(searchResults).reduce((total, dirResults) => {
            return (
                total +
                Object.values(dirResults).reduce((dirTotal, textFiles) => {
                    return dirTotal + processFilePaths(textFiles, splitArray).length;
                }, 0)
            );
        }, 0)},
        mappingDefineMatches: ${allMappingResults.length}
    },
${Object.entries(searchResults)
    .map(
        ([dir, dirData]) =>
            `    '${dir}': {\n${Object.entries(dirData)
                .map(([text, textFiles]) => {
                    const processedPaths = processFilePaths(textFiles, splitArray);
                    // 只获取与当前搜索文本相关的MappingDefine结果
                    const textMappingResults = allMappingResults.filter(result => result.foundPaths.some(path => processedPaths.includes(path)));
                    return `        '${text}': {\n            filesArray: ${JSON.stringify(
                        processedPaths,
                        null,
                        16
                    )},\n\n            mappingDefineResults: ${JSON.stringify(textMappingResults, null, 16)}\n        }`;
                })
                .join(',\n')}\n    }`
    )
    .join(',\n')}
};

module.exports = searchResults;
`;

    const outputPath = path.join(__dirname, 'results.combined_search.js');
    fsSync.writeFileSync(outputPath, content, 'utf8');
    console.log(`\n📄 已生成合并结果文件: ${outputPath}`);
    console.log(`📊 总共找到 ${totalFiles} 个文件`);
    console.log(`📊 包含 ${allMappingResults.length} 个MappingDefine匹配项`);

    return outputPath;
}

/**
 * 递归查找MappingDefine*.js文件
 * @param {string} projectDir - 项目目录
 * @returns {Array} - MappingDefine文件路径数组
 */
async function findMappingDefineFiles(projectDir) {
    const mappingFiles = [];

    async function searchDirectory(dir) {
        try {
            const items = await fs.readdir(dir);

            await Promise.all(
                items.map(async item => {
                    const fullPath = path.join(dir, item);

                    try {
                        const stat = await fs.stat(fullPath);

                        if (stat.isDirectory()) {
                            await searchDirectory(fullPath);
                        } else if (stat.isFile() && item.includes('MappingDefine') && item.endsWith('.js')) {
                            mappingFiles.push(fullPath);
                        }
                    } catch (error) {
                        // 忽略无法访问的文件/目录
                    }
                })
            );
        } catch (error) {
            // 忽略无法读取的目录
        }
    }

    await searchDirectory(projectDir);
    return mappingFiles;
}

/**
 * 检查MappingDefine文件是否包含results.mapping_array中的路径
 * @param {string} mappingFile - MappingDefine文件路径
 * @param {Array} filePaths - 要检查的文件路径数组
 * @returns {Array} - 找到的匹配路径
 */
async function checkMappingFileContainsPath(mappingFile, filePaths) {
    try {
        const content = await fs.readFile(mappingFile, 'utf8');
        const foundPaths = [];

        filePaths.forEach(filePath => {
            // 直接使用处理后的路径进行匹配
            if (content.includes(filePath) || content.includes(filePath.replace(/\//g, '\\/')) || content.includes(filePath.replace(/\//g, '\\\\'))) {
                foundPaths.push(filePath);
            }
        });

        return foundPaths;
    } catch (error) {
        console.warn(`无法读取文件: ${mappingFile}`);
        return [];
    }
}

/**
 * 在自定义项目中搜索MappingDefine文件
 * @param {Array} customProjects - 自定义项目路径数组
 * @param {Array} filePaths - 要搜索的文件路径数组
 * @returns {Array} - 搜索结果
 */
async function searchInCustomProjects(customProjects, filePaths) {
    console.log('\n' + '='.repeat(60));
    console.log('🔍 开始在自定义项目中搜索MappingDefine文件...');
    console.log('='.repeat(60));

    if (!customProjects || customProjects.length === 0) {
        console.log('⚠️  未配置customProjects，跳过MappingDefine搜索');
        return [];
    }

    if (filePaths.length === 0) {
        console.log('⚠️  results.mapping_array为空，跳过MappingDefine搜索');
        return [];
    }

    console.log(`📁 待搜索项目数量: ${customProjects.length}`);
    console.log(`📄 待匹配文件数量: ${filePaths.length}`);
    console.log('');

    const results = [];

    // 遍历每个自定义项目
    for (const projectPath of customProjects) {
        const projectName = path.basename(projectPath);
        console.log(`🔍 搜索项目: ${projectName}`);

        try {
            // 检查项目目录是否存在
            await fs.access(projectPath);

            // 查找MappingDefine文件
            const mappingFiles = await findMappingDefineFiles(projectPath);
            console.log(`  📋 找到 ${mappingFiles.length} 个MappingDefine文件`);

            // 检查每个MappingDefine文件
            for (const mappingFile of mappingFiles) {
                const foundPaths = await checkMappingFileContainsPath(mappingFile, filePaths);

                if (foundPaths.length > 0) {
                    const relativeMappingFile = mappingFile.replace(projectPath, '');
                    console.log(`  ✅ ${relativeMappingFile} 包含 ${foundPaths.length} 个匹配路径`);

                    results.push({
                        project: projectName,
                        mappingFile: mappingFile,
                        foundPaths: foundPaths
                    });
                }
            }
        } catch (error) {
            console.log(`  ❌ 项目目录不存在: ${projectPath}`);
        }

        console.log('');
    }

    return results;
}

/**
 * 生成MappingDefine搜索结果报告
 * @param {Array} results - 搜索结果
 */
function generateMappingDefineReport(results) {
    console.log('='.repeat(60));
    console.log('📊 MappingDefine搜索结果汇总');
    console.log('='.repeat(60));

    if (results.length === 0) {
        console.log('❌ 未在任何自定义项目的MappingDefine文件中找到匹配的路径');
        return;
    }

    console.log(`✅ 在 ${results.length} 个MappingDefine文件中找到匹配项:`);
    console.log('');

    results.forEach((result, index) => {
        console.log(`${index + 1}. 项目: ${result.project}`);
        console.log(`   文件: ${result.mappingFile}`);
        console.log(`   匹配路径 (${result.foundPaths.length}个):`);
        result.foundPaths.forEach(foundPath => {
            console.log(`     - ${foundPath}`);
        });
        console.log('');
    });
}

/**
 * 处理文件路径：基于splitArray截取并去除后缀
 * @param {Array} filePaths - 原始文件路径数组
 * @param {Array} splitArray - 用于截取路径的分割点数组
 * @returns {Array} - 处理后的路径数组
 */
function processFilePaths(filePaths, splitArray) {
    if (!splitArray || splitArray.length === 0) {
        // 如果没有配置splitArray，直接去除后缀
        return filePaths.map(filePath => {
            const pathWithoutExt = path.parse(filePath).dir + '/' + path.parse(filePath).name;
            return pathWithoutExt;
        });
    }

    return filePaths.map(filePath => {
        let processedPath = filePath;
        let foundSplitPoint = false;

        // 按顺序尝试splitArray中的每个分割点，找到第一个能分割的就停止
        for (const splitPoint of splitArray) {
            const splitIndex = filePath.indexOf(splitPoint);
            if (splitIndex !== -1) {
                processedPath = filePath.substring(splitIndex);
                foundSplitPoint = true;
                break; // 找到第一个能分割的就停止，不再尝试后续的分割点
            }
        }

        // 如果未找到分割点，使用完整路径
        if (!foundSplitPoint) {
            processedPath = filePath;
        }

        // 去除文件后缀
        const parsedPath = path.parse(processedPath);
        return parsedPath.dir + '/' + parsedPath.name;
    });
}

/**
 * 主函数 - 使用async/await确保正确的执行顺序
 */
async function main() {
    try {
        const config = loadSearchConfig();

        if (!config) {
            console.error('❌ 无法读取搜索配置，请检查config.customSearchDirAndText.js文件');
            return;
        }

        const { searchDir, searchText, fileExtensions, splitArray, customProjects } = config;

        console.log('='.repeat(60));
        console.log('🔍 开始搜索文件...');
        console.log(`📁 搜索目录: ${searchDir.join(', ')}`);
        console.log(`🔤 搜索文本: ${searchText.join(', ')}`);
        console.log(`📋 文件类型: ${fileExtensions.join(', ')}`);
        console.log(`✂️  路径截取: ${splitArray.join(', ')}`);
        console.log('='.repeat(60));

        const searchResults = {};

        // 遍历所有搜索目录
        for (const dir of searchDir) {
            try {
                await fs.access(dir);
                console.log(`\n正在搜索目录: ${dir}`);

                searchResults[dir] = {};

                // 为每个搜索文本独立搜索
                for (const text of searchText) {
                    console.log(`  搜索文本: "${text}"`);
                    const matchedFiles = await findFilesWithSingleText(dir, text, fileExtensions);
                    searchResults[dir][text] = matchedFiles;
                    console.log(`  找到 ${matchedFiles.length} 个匹配文件`);
                }
            } catch (error) {
                console.error(`❌ 搜索目录不存在: ${dir}`);
            }
        }

        // 为每个搜索文本独立进行MappingDefine搜索
        const allMappingResults = [];

        for (const dir of searchDir) {
            if (!searchResults[dir]) continue;

            for (const text of searchText) {
                const textFiles = searchResults[dir][text] || [];
                const processedPaths = processFilePaths(textFiles, splitArray);

                if (processedPaths.length > 0) {
                    console.log(`\n🔍 为搜索文本 "${text}" 搜索MappingDefine文件...`);
                    const textMappingResults = await searchInCustomProjects(customProjects, processedPaths);
                    allMappingResults.push(...textMappingResults);
                }
            }
        }

        const outputPath = generateCombinedResultsFile(searchResults, searchText, searchDir, splitArray, allMappingResults);

        generateMappingDefineReport(allMappingResults);

        console.log('\n' + '='.repeat(60));
        console.log('✅ 搜索完成!');
        console.log(`📊 每个搜索文本的结果相互独立`);
        if (allMappingResults.length > 0) {
            console.log(`📊 在 ${allMappingResults.length} 个MappingDefine文件中找到匹配项`);
        }
        console.log(`📄 合并结果已保存到: ${outputPath}`);
        console.log('='.repeat(60));
    } catch (error) {
        console.error('❌ 执行过程中发生错误:', error.message);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    findFilesWithSingleText,
    loadSearchConfig,
    processFilePaths,
    findMappingDefineFiles,
    checkMappingFileContainsPath,
    searchInCustomProjects
};
