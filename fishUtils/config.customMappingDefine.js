/**
 * 自定义组件映射定义
 * 用于定义旧路径到新标准化路径的映射关系
 * 格式: {
 *   "组件名称": {
 *     "旧路径": "新标准化路径"
 *   }
 * }
 */

/* eslint-disable no-undef */

module.exports = {
    searchDir: ['/Users/<USER>/Documents/my-work-projects/coc92-core/COC/web/fish-web/crm/crm-portal/src/main/webapp/'],
    searchComponents: {
        /**
         * 添加联系人组件
         * 从fix模块迁移到标准化组件
         */
        AddContactManComponent: {
            'crm/modules/fix/InstallInfo/views/component/AddContactManComponent':
                'crm/standardizedcomponents/fix/InstallInfo/views/component/AddContactManComponent'
        },

        /**
         * 订单视图组件
         * 从pos模块迁移到标准化组件
         */
        OrderView: {
            'crm/modules/pos/orderview/views/OrderView': 'crm/standardizedcomponents/pos/orderview/views/OrderView'
        },

        /**
         * 客户基本信息视图组件
         * 从pos客户模块迁移到标准化组件
         */
        CustBasicInfoView: {
            'crm/modules/pos/cust/views/CustBasicInfoView': 'crm/standardizedcomponents/pos/cust/views/CustBasicInfoView'
        },

        /**
         * 选择号码弹窗视图组件
         * 从pos订单录入模块迁移到标准化组件通用视图
         */
        ChooseNumberPopView: {
            'crm/modules/pos/orderentry/views/popwin/ChooseNumberPopView': 'crm/standardizedcomponents/views/ChooseNumberPopView'
        },

        /**
         * 选择号码弹窗视图组件
         * 从pos订单录入模块迁移到标准化组件通用视图
         */
        SelectNumberPopView: {
            'crm/modules/pos/orderentry/views/popwin/SelectNumberPopView': 'crm/standardizedcomponents/views/SelectNumberPopView'
        }
    }
};
