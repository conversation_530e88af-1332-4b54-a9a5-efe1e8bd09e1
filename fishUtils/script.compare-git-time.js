/**
 * 比较标准化组件的Git提交时间
 * 用于检查旧组件是否被更新，但新组件没有同步更新的情况
 * 依赖Git命令行工具
 * 依赖config.customMappingDefine.js文件
 */

/* eslint-disable no-undef, @typescript-eslint/no-require-imports */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 获取文件的最后一次Git提交时间
 * @param {string} ifilePath - 显示用的文件路径
 * @param {string} filePath - 实际文件路径
 * @param {string} gitWorkDir - Git工作目录
 * @returns {number|null} - Unix时间戳，失败返回null
 */
function getLastCommitTime(ifilePath, filePath, gitWorkDir) {
    try {
        // 如果路径没有后缀，默认添加.js后缀
        let actualPath = filePath;
        if (!path.extname(filePath)) {
            actualPath = filePath + '.js';
        }

        // 计算相对于Git工作目录的路径
        const relativePath = path.relative(gitWorkDir, actualPath);
        const command = `git log -1 --format="%ct" -- "${relativePath}"`;
        
        const timestamp = execSync(command, {
            encoding: 'utf8',
            cwd: gitWorkDir  // 在B项目目录下执行Git命令
        }).trim();

        if (!timestamp) {
            console.warn(`文件 ${ifilePath} 没有Git提交历史`);
            return null;
        }

        return parseInt(timestamp);
    } catch (error) {
        console.error(`获取文件 ${ifilePath} 的提交时间失败:`, error.message);
        return null;
    }
}

/**
 * 检查文件是否存在
 * @param {string} filePath - 文件路径
 * @returns {boolean}
 */
function fileExists(filePath) {
    try {
        // 如果路径没有后缀，默认添加.js后缀
        let actualPath = filePath;
        if (!path.extname(filePath)) {
            actualPath = filePath + '.js';
        }
        return fs.existsSync(actualPath);
    } catch (error) {
        console.log('error', error);
        return false;
    }
}

/**
 * 从config.customMappingDefine.js读取文件映射配置
 * @returns {Object} - 包含searchComponents和searchDir的配置对象
 */
function loadCustomMappingDefine() {
    try {
        // 清除require缓存以确保获取最新配置
        delete require.cache[require.resolve('./config.customMappingDefine.js')];
        const config = require('./config.customMappingDefine.js');

        return {
            searchComponents: config.searchComponents || {},
            searchDir: config.searchDir || []
        };
    } catch (error) {
        console.error('读取config.customMappingDefine.js失败:', error.message);
        return {
            searchComponents: {},
            searchDir: []
        };
    }
}

/**
 * 比较标准化组件的Git提交时间
 * @param {Object} customMapping - 自定义映射配置 {"组件名": {"旧路径": "新路径"}}
 * @param {string} baseDir - 基础目录路径（B项目路径）
 * @returns {Array} - 符合条件的组件列表
 */
function compareStandardizedComponents(customMapping, baseDir) {
    const results = [];

    console.log('='.repeat(50));
    console.log('');
    console.log('开始比较标准化组件Git提交时间...\n');
    console.log(`目标项目路径: ${baseDir}`);
    console.log('');

    for (const [componentName, pathMapping] of Object.entries(customMapping)) {
        console.log(`检查组件: ${componentName}`);

        for (const [iOldPath, iNewPath] of Object.entries(pathMapping)) {
            const oldPath = path.join(baseDir, iOldPath);
            const newPath = path.join(baseDir, iNewPath);

            // 检查文件存在性
            if (!fileExists(oldPath)) {
                console.log(`  ❌ 旧文件不存在: ${iOldPath}`);
                continue;
            }
            if (!fileExists(newPath)) {
                console.log(`  ❌ 新文件不存在: ${iNewPath}`);
                continue;
            }

            // 获取提交时间，传入B项目路径作为Git工作目录
            const oldTime = getLastCommitTime(iOldPath, oldPath, baseDir);
            const newTime = getLastCommitTime(iNewPath, newPath, baseDir);

            if (oldTime === null || newTime === null) {
                console.log(`  ❌ 无法获取提交时间`);
                continue;
            }

            // 转换为可读时间
            const oldDate = new Date(oldTime * 1000);
            const newDate = new Date(newTime * 1000);

            // 比较时间：如果旧路径的时间晚于或等于新路径，输出组件信息
            if (oldTime >= newTime) {
                console.log(`  ✅ 旧文件时间晚于或等于新文件，组件: ${componentName}`);
                results.push({
                    componentName,
                    oldPath: iOldPath,  // 保存相对路径用于显示
                    newPath: iNewPath,  // 保存相对路径用于显示
                    oldTime: oldDate.toLocaleString(),
                    newTime: newDate.toLocaleString()
                });
            }
        }
        console.log('');
    }

    return results;
}

/**
 * 主函数
 */
function main() {
    // 从A项目的config.customMappingDefine.js读取配置
    const config = loadCustomMappingDefine();

    if (Object.keys(config.searchComponents).length === 0) {
        console.log('未找到有效的映射配置，退出程序');
        return;
    }

    // 获取B项目的基础目录
    const baseDir = Array.isArray(config.searchDir) ? config.searchDir[0] : config.searchDir;
    if (!baseDir) {
        console.log('未找到有效的搜索目录配置，退出程序');
        return;
    }

    // 检查B项目目录是否存在
    if (!fs.existsSync(baseDir)) {
        console.error(`❌ B项目目录不存在: ${baseDir}`);
        console.error('请检查config.customMappingDefine.js中的searchDir配置');
        return;
    }

    console.log('='.repeat(60));
    console.log('Git提交时间比较工具');
    console.log('='.repeat(60));
    console.log(`A项目（脚本执行位置）: ${process.cwd()}`);
    console.log(`B项目（分析目标）: ${baseDir}`);
    console.log('');
    console.log('加载的映射配置:');
    console.log(JSON.stringify(config.searchComponents, null, 2));
    console.log('');

    // 执行比较
    const outdatedComponents = compareStandardizedComponents(config.searchComponents, baseDir);

    // 输出结果
    console.log('='.repeat(50));
    console.log('比较结果汇总:');
    console.log(`修改时间晚于新文件的旧文件总数量: ${outdatedComponents.length}`);

    if (outdatedComponents.length > 0) {
        console.log(`找到 ${outdatedComponents.length} 个需要关注的组件:`);
        outdatedComponents.forEach((component, index) => {
            console.log(`${index + 1}. 组件: ${component.componentName}`);
            console.log(`   旧路径: ${component.oldPath} (${component.oldTime})`);
            console.log(`   新路径: ${component.newPath} (${component.newTime})`);
            console.log('');
        });

        console.log('建议操作:');
        console.log('以上组件的旧文件可能包含更新的内容，建议检查是否需要同步到标准化组件');

        // 生成结果文件到A项目
        const resultFile = generateCompareResultsFile(outdatedComponents, config.searchComponents);
        console.log(`\n✅ 详细结果已保存到A项目，可通过以下方式查看:`);
        console.log(`   require('${resultFile.replace(process.cwd(), '.')}')`);
    } else {
        console.log('所有标准化组件都是最新的');

        // 即使没有需要关注的组件，也生成一个空结果文件
        generateCompareResultsFile([], config.searchComponents);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = {
    getLastCommitTime,
    compareStandardizedComponents,
    fileExists,
    loadCustomMappingDefine,
    generateCompareResultsFile
};

/**
 * 生成比较结果报告文件
 * @param {Array} outdatedComponents - 需要关注的组件列表
 * @param {Object} customMapping - 映射配置
 */
function generateCompareResultsFile(outdatedComponents, customMapping) {
    const reportContent = `/**
 * Git提交时间比较结果
 * 生成时间: ${new Date().toLocaleString()}
 * 比较的组件总数: ${Object.keys(customMapping).length}
 * 需要关注的组件数量: ${outdatedComponents.length}
 * 
 * 说明: 以下组件的旧文件最后提交时间晚于或等于新文件，可能需要同步更新
 */

/* eslint-disable no-undef */

const gitCompareResults = {
    /**
     * 生成信息
     */
    metadata: {
        generateTime: "${new Date().toISOString()}",
        totalComponents: ${Object.keys(customMapping).length},
        outdatedCount: ${outdatedComponents.length}
    },
    
    /**
     * 需要关注的组件列表
     * 每个组件包含: 组件名、旧路径、新路径、提交时间等信息
     */
    outdatedComponents: ${JSON.stringify(outdatedComponents, null, 8)}
};

module.exports = gitCompareResults;
`;

    const outputPath = path.join(__dirname, 'results.git_compare.js');
    fs.writeFileSync(outputPath, reportContent, 'utf8');
    console.log(`📄 比较结果已保存到: ${outputPath}`);

    return outputPath;
}
