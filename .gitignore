# Files
.DS_Store
.ruby-version
test.sass
npm-debug.log
yarn.lock

.history/


# Folders
.idea/
.sass-cache
gh_pages
_site
**/node_modules
/dist
/lib
**/esm
**/lib
/sdk
/public
/gh-pages
/.vscode/*
/output
/toolkit/amis-renderer
/toolkit/output
/coverage
/npm
/mock/cfc/cfc.zip
.rollup.cache
/theme-npm

dist
tsconfig.tsbuildinfo
lerna-debug.log
.rollup.cache
package-lock.json
revision.json
**/revision.json
~$*


!/.vscode/iconConfig.json
publish.sh

newFishCssFiles

.env.development

fishUtils/results.*.js