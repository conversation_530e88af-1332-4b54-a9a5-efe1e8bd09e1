import { BasePlugin, registerEditorPlugin, EditorManager, getSchemaTpl, ScaffoldForm } from "amis-editor-core";

export class RfsAttributePlugin extends BasePlugin {
  static id = 'RfsAttributePlugin';

  name = 'RfsAttribute';

  panelTitle = 'RfsAttribute';

  rendererName = 'rfs-attribute';

  $schema = '/schemas/RfsAttributePlugin.json'; 

  isBaseComponent = false;

  description =
    '生成RFS属性';

  scaffold = {
    type: 'rfs-attribute',
    body: [],
  };

  previewSchema = {
    type: 'rfs-attribute',
    body: [],
  };

  panelBodyCreator = (context: any) => {
    return [
      getSchemaTpl('tabs', [
        {
          title: '属性',
          body: getSchemaTpl(
            'collapseGroup',
            [
              {
                title: '基本',
                body: [
                  {
                    name: 'title',
                    type: 'input-text',
                    label: '标题',
                  },
                ]
              }
            ]
          ),
        },
      ]),
    ];
  }

  get scaffoldForm(): ScaffoldForm {
    return {
      title: 'RFS Attribute Guide',
      size: "lg",
      body: [
        {
          type: 'entity-attribute-guide',
          entityType: 'rfs',
        }
      ],
      canRebuild: true,
    }
  }

  

  constructor(manager: EditorManager) {
    super(manager);
  }
}

registerEditorPlugin(RfsAttributePlugin);