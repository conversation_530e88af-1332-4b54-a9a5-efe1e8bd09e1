import { BasePlugin, registerEditorPlugin, EditorManager, getSchemaTpl, ScaffoldForm } from "amis-editor-core";

export class ResAttributePlugin extends BasePlugin {
  static id = 'ResAttributePlugin';

  name = 'ResAttribute';

  panelTitle = 'ResAttribute';

  rendererName = 'res-attribute';

  $schema = '/schemas/ResAttributePlugin.json'; 

  isBaseComponent = false;

  description =
    '生成RES属性';

  scaffold = {
    type: 'res-attribute',
    body: [],
  };

  previewSchema = {
    type: 'res-attribute',
    body: [],
  };

  panelBodyCreator = (context: any) => {
    return [
      getSchemaTpl('tabs', [
        {
          title: '属性',
          body: getSchemaTpl(
            'collapseGroup',
            [
              {
                title: '基本',
                body: [
                  {
                    name: 'title',
                    type: 'input-text',
                    label: '标题',
                  },
                ]
              }
            ]
          ),
        },
      ]),
    ];
  }

  get scaffoldForm(): ScaffoldForm {
    return {
      title: 'RES Attribute Guide',
      size: "lg",
      body: [
        {
          type: 'entity-attribute-guide',
          entityType: 'res',
        }
      ],
      canRebuild: true,
    }
  }

  

  constructor(manager: EditorManager) {
    super(manager);
  }
}

registerEditorPlugin(ResAttributePlugin);