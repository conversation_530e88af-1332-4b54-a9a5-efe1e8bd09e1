import { BasePlugin, registerEditorPlugin, EditorManager, getSchemaTpl, ScaffoldForm } from "amis-editor-core";

export class ServiceOrderAttributePlugin extends BasePlugin {
  static id = 'ServiceOrderAttributePlugin';

  name = 'ServiceOrderAttribute';

  panelTitle = 'ServiceOrderAttribute';

  rendererName = 'service-order-attribute';

  $schema = '/schemas/ServiceOrderAttributePlugin.json'; 

  isBaseComponent = false;

  description =
    '生成ServiceOrderAttribute属性';

  scaffold = {
    type: 'service-order-attribute',
    body: [],
  };

  previewSchema = {
    type: 'service-order-attribute',
    body: [],
  };

  panelBodyCreator = (context: any) => {
    return [
      getSchemaTpl('tabs', [
        {
          title: '属性',
          body: getSchemaTpl(
            'collapseGroup',
            [
              {
                title: '基本',
                body: [
                  {
                    name: 'title',
                    type: 'input-text',
                    label: '标题',
                  },
                ]
              }
            ]
          ),
        },
      ]),
    ];
  }

  get scaffoldForm(): ScaffoldForm {
    return {
      title: 'Service Order Attribute Guide',
      size: "lg",
      body: [
        {
          type: 'entity-attribute-guide',
          entityType: 'so',
        }
      ],
      canRebuild: true,
    }
  }

  

  constructor(manager: EditorManager) {
    super(manager);
  }
}

registerEditorPlugin(ServiceOrderAttributePlugin);