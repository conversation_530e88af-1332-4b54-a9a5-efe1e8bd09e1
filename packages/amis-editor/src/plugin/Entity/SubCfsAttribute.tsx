import { BasePlugin, registerEditorPlugin, EditorManager, getSchemaTpl, ScaffoldForm } from "amis-editor-core";

export class SubCfsAttributePlugin extends BasePlugin {
  static id = 'SubCfsAttributePlugin';

  name = 'SubCfsAttribute';

  panelTitle = 'SubCfsAttribute';

  rendererName = 'sub-cfs-attribute';

  $schema = '/schemas/SubCfsAttributePlugin.json'; 

  isBaseComponent = false;

  description =
    '生成SUB_CFS属性';

  scaffold = {
    type: 'sub-cfs-attribute',
    body: [],
  };

  previewSchema = {
    type: 'sub-cfs-attribute',
    body: [],
  };

  panelBodyCreator = (context: any) => {
    return [
      getSchemaTpl('tabs', [
        {
          title: '属性',
          body: getSchemaTpl(
            'collapseGroup',
            [
              {
                title: '基本',
                body: [
                  {
                    name: 'title',
                    type: 'input-text',
                    label: '标题',
                  },
                ]
              }
            ]
          ),
        },
      ]),
    ];
  }

  get scaffoldForm(): ScaffoldForm {
    return {
      title: 'Sub CFS Attribute Guide',
      size: "lg",
      body: [
        {
          type: 'entity-attribute-guide',
          entityType: 'sub-cfs',
        }
      ],
      canRebuild: true,
    }
  }

  

  constructor(manager: EditorManager) {
    super(manager);
  }
}

registerEditorPlugin(SubCfsAttributePlugin);