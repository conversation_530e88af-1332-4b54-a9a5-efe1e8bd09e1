import { BasePlugin, registerEditorPlugin, EditorManager, getSchemaTpl, ScaffoldForm } from "amis-editor-core";

export class CfsAttributePlugin extends BasePlugin {
  static id = 'CfsAttributePlugin';

  name = 'CfsAttribute';

  panelTitle = 'CfsAttribute';

  rendererName = 'cfs-attribute';

  $schema = '/schemas/CfsAttributePlugin.json'; // 配置后，选中该组件时，左侧code tab栏显示。不配置则不显示

  isBaseComponent = false;
  isBusinessComponent = true;

  description =
    '生成CFS属性';

  scaffold = {
    type: 'cfs-attribute',
    body: [],
  };

  previewSchema = {
    type: 'cfs-attribute',
    body: [],
  };

  panelBodyCreator = (context: any) => {
    return [
      getSchemaTpl('tabs', [
        {
          title: '属性',
          body: getSchemaTpl(
            'collapseGroup',
            [
              {
                title: '基本',
                body: [
                  {
                    name: 'title',
                    type: 'input-text',
                    label: '标题',
                  },
                ]
              }
            ]
          ),
        },
      ]),
    ];
  }

  get scaffoldForm(): ScaffoldForm {
    return {
      title: 'CFS Attribute Guide',
      size: "lg",
      body: [
        {
          type: 'entity-attribute-guide',
          entityType: 'cfs'
        }
      ],
      pipeOut: (value: any) => {
        console.log('pipeOut-value', value);
        return value;
      },
      canRebuild: true,
    }
  }

  

  constructor(manager: EditorManager) {
    super(manager);
  }
}

registerEditorPlugin(CfsAttributePlugin);