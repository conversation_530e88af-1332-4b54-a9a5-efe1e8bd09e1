import {
  BaseEventContext,
  BasePlugin,
  getSchemaTpl,
  registerEditorPlugin,
  RendererPluginEvent,
} from 'amis-editor-core';
import thumbImg from '../../../icons/thumb/Agreement.png';
import { getEventControlConfig } from '../../../renderer/event-control/helper';
import apiDoc from './ApiDoc/Agreement_API.md';
import { mockData } from './mocks/agreement.mock';

export class AgreementPlugin extends BasePlugin {
  static id = 'AgreementPlugin';
  rendererName = 'crm-agreement'; // 这里需要和注册的渲染器名称保持一致
  $schema = '/schemas/AgreementSchema.json'; // 目前还不清楚这个字段的作用

  tags = ['COC(Fish)']; // 组名称
  name = '协议';
  isBaseComponent = false; // 控制渲染器在编辑器组件面板中的位置
  isBusinessComponent = true;
  description = '协议组件';

  // 当组件拖入编辑器时，默认插入的schema，参数会作为props传递给组件
  scaffold = {
    type: 'crm-agreement',
    name: 'agreement',
    required: false,
    showCheckbox: true,
    mockData: mockData,
    editorSetting: {
      mock: {},
    },
    apiDoc,
    thumb: thumbImg,
  };

  previewSchema = {
    ...this.scaffold,
  };

  panelTitle = '协议组件';

  // 事件定义
  events: RendererPluginEvent[] = [];

  panelBodyCreator = (context: BaseEventContext) => {
    return [
      getSchemaTpl('tabs', [
        {
          title: '属性',
          body: getSchemaTpl('collapseGroup', [
            {
              title: '基本',
              body: [
                getSchemaTpl('layout:originPosition', { value: 'left-top' }),
                getSchemaTpl('formItemName', {
                  required: true,
                }),
                getSchemaTpl('label'),
                getSchemaTpl('labelRemark'),
                getSchemaTpl('remark'),
                getSchemaTpl('placeholder'),
                getSchemaTpl('description'),
              ],
            },
            {
              title: '协议设置',
              body: [
                {
                  type: 'input-text',
                  label: '协议标题',
                  name: 'agreementTitle',
                  placeholder: '请输入协议标题',
                },
                {
                  type: 'textarea',
                  label: '协议内容',
                  name: 'agreementContent',
                  placeholder: '请输入协议内容',
                  minRows: 3,
                  maxRows: 10,
                },
                {
                  type: 'switch',
                  label: '是否必须同意',
                  name: 'required',
                  value: false,
                },
                {
                  type: 'switch',
                  label: '显示复选框',
                  name: 'showCheckbox',
                  value: true,
                },
              ],
            },
          ]),
        },
        {
          title: '外观',
          body: getSchemaTpl('collapseGroup', [
            getSchemaTpl('style:formItem', {
              renderer: context.info.renderer,
            }),
            getSchemaTpl('style:classNames'),
          ]),
        },
        {
          title: '事件',
          className: 'p-none',
          body: [
            getSchemaTpl('eventControl', {
              name: 'onEvent',
              ...getEventControlConfig(this.manager, context),
            }),
          ],
        },
      ]),
    ];
  };
}

registerEditorPlugin(AgreementPlugin);
