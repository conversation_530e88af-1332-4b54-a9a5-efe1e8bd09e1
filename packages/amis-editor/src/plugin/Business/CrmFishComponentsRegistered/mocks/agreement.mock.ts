export const mockData = {
  pageUuid: 'qUdFL5i7J31gkosdzmX9j9AiepdW3f12',
  orderItem: {
    SERV_TYPE: 15,
    DP_OFFER_ORDER: [
      {
        SP_ID: 0,
        SUBSCRIBE_METHOD: 'OFFER_GROUP_DEFAULT',
        OFFER_TYPE: '4',
        EFF_TYPE: 'B',
        OFFER_GROUP_ID: 100178,
        OFFER_ID: 100530,
        OFFER_SEQ: '495acaf4-727c-4a54-9850-ecb158d259ef',
        DUPLICATE_FLAG: 'C',
        TRIGGER_OBJ_ID: 100178,
        DEFAULT_FLAG: 'Y',
        OFFER_CODE: '100530',
        OFFER_NAME: 'Connect Watch 10',
        OPERATION_TYPE: 'A',
        EXITS_AGREEMENT_FLAG: 'N',
        MRC_LIST_PRICE: 1000,
        ORDER_ITEM_ID: ****************,
        HIDE_FLAG: 'N',
      },
      {
        SP_ID: 0,
        SUBSCRIBE_METHOD: 'OFFER_GROUP_DEFAULT',
        OFFER_TYPE: '4',
        EFF_TYPE: 'B',
        OFFER_GROUP_ID: 100179,
        OFFER_ID: 100536,
        OFFER_SEQ: '012940e0-f70a-4046-947d-f161ab8122b1',
        TRIGGER_OBJ_ID: 100179,
        DEFAULT_FLAG: 'Y',
        OFFER_CODE: '100536',
        OFFER_NAME: 'Foreigner Deposit 500',
        OPERATION_TYPE: 'A',
        EXITS_AGREEMENT_FLAG: 'N',
        ORDER_ITEM_ID: ****************,
        OTC_LIST_PRICE: 50000,
        HIDE_FLAG: 'N',
      },
      {
        SP_ID: 0,
        SUBSCRIBE_METHOD: 'OFFER_GROUP_DEFAULT',
        OFFER_TYPE: '4',
        EFF_TYPE: 'B',
        OFFER_GROUP_ID: 100181,
        OFFER_ID: 100544,
        OFFER_SEQ: '59df8ed7-7919-41ed-8444-9664368a7343',
        TRIGGER_OBJ_ID: 100181,
        DP_OFFER_ORDER_ATTR: [
          {
            ATTR_NAME: 'Monthly Rent Charging Type',
            OPERATION_TYPE: 'A',
            VISIBLE: 'N',
            VALUE_MARK: 'Prorate For All',
            ATTR_VALUE: 'P',
            ATTR_ID: 100752,
          },
        ],
        DEFAULT_FLAG: 'Y',
        OFFER_CODE: 'DP100543',
        OFFER_NAME: 'Ocean Postpaid 138',
        OPERATION_TYPE: 'A',
        EXITS_AGREEMENT_FLAG: 'N',
        MRC_LIST_PRICE: 13800,
        ORDER_ITEM_ID: ****************,
        OTC_LIST_PRICE: 13800,
        HIDE_FLAG: 'Y',
      },
    ],
    ACCT: {
      PAYMENT_METHOD_ID: 1,
      CUST_ID: *********,
      ACCT_ID: *********,
      STATE: 'A',
      BILLING_CYCLE_TYPE_ID: 15600,
      ACCT_NAME: 'zhg071002',
      ACCT_NBR: '***********',
      POSTPAID: 'Y',
    },
    SUBS_PLAN_NAME: 'Ocean Postpaid 138',
    CUST_ORDER_ID: ****************,
    CUST_INFO: {
      CUST_ID: *********,
      CUST_TYPE: 'A',
    },
    OFFER_ID: 100380,
    INDEP_PROD_ORDER_ATTR: [
      {
        ATTR_VALUE: 'MOBILE_VD',
        ROUTING_ID: 1,
        SP_ID: 0,
        ATTR_ID: 101260,
        ORDER_ITEM_ID: ****************,
        OPERATION_TYPE: 'A',
        PART_ID: 307,
      },
      {
        ATTR_VALUE: '111111',
        ROUTING_ID: 1,
        SP_ID: 0,
        ATTR_ID: 510040,
        ORDER_ITEM_ID: ****************,
        OPERATION_TYPE: 'A',
        PART_ID: 307,
      },
      {
        ATTR_VALUE: '111111',
        ROUTING_ID: 1,
        SP_ID: 0,
        ATTR_ID: 510041,
        ORDER_ITEM_ID: ****************,
        OPERATION_TYPE: 'A',
        PART_ID: 307,
      },
    ],
    SUBS_EVENT_ID: 1,
    PARTY_TYPE: 'A',
    ORDER_NBR: '****************',
    ACCT_ID: *********,
    IS_RESERVE: false,
    ORDER_TYPE: 'B',
    OPERATION_TYPE: 'A',
    PART_ID: 307,
    IS_BOOKING: true,
    SUBS_PLAN_ID: 100543,
    CUST_ID: *********,
    STAFF_ID: 1,
    SP_ID: 0,
    SIM_TYPE_ID: 1,
    ROUTING_ID: 1,
    STATE_DATE: '2025-07-24 14:40:34',
    ORDER_STATE: 'I',
    RES_RESERVE_INST_ID: '****************',
    POSTPAID: 'Y',
    SUBS_BASE_ORDER: {
      SERV_TYPE: 15,
      CUST_ID: *********,
      ORG_ID: 1,
      USER_ID: *********,
      SUBS_PLAN_NAME: 'Ocean Postpaid 138',
      AREA_NAME: 'Default Area(Please modify)',
      ROUTING_ID: 1,
      DEF_LANG_ID: 2,
      POSTPAID: 'Y',
      AREA_ID: 1,
      DEF_LANG_NAME: 'English',
      INDEP_PROD_SPEC_ID: 100380,
      ACCT_ID: *********,
      USER_NAME: 'zhg0710',
      OPERATION_TYPE: 'A',
      PART_ID: 307,
      SUBS_PLAN_ID: 100543,
      ORDER_ITEM_ID: ****************,
      ACCT_NBR: '***********',
      CUST_NAME: 'zhg0710',
    },
    BUNDLE_MEMBER_FLAG: 'N',
    SUBS_ID: -********,
    OFFER_NAME: 'Mobile',
    IS_JOB_OPERATION: false,
    ORDER_ITEM_ID: ****************,
    CUST_NAME: 'zhg0710',
    PARTY_CODE: '1',
    url: 'crm/modules/pos/flowpage/business/newconnection/views/GSMNewSubsPlanView',
  },
  HANDLED_BY_ACCT_MANAGER: 'N',
};
