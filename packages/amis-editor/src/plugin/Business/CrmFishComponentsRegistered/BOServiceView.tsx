import { render as amisRender } from 'amis';
import {
  BaseEventContext,
  BasePlugin,
  getSchemaTpl,
  RegionConfig,
  registerEditorPlugin,
  RendererPluginEvent,
} from 'amis-editor-core';
import { getEventControlConfig } from '../../../renderer/event-control/helper';
import apiDoc from './ApiDoc/BOServiceView_API.md';
import { mockData } from './mocks/boService.mock';

export class BOServicePlugin extends BasePlugin {
  static id = 'BOServicePlugin';
  // 关联渲染器名字
  rendererName = 'crm-bo-service';

  name = 'BO Service';

  panelTitle = 'BO Service';

  icon = 'fa fa-server';

  pluginIcon = 'service-plugin';

  panelIcon = 'service-plugin';

  $schema = '/schemas/ServiceSchema.json';

  isBaseComponent = false;

  isBusinessComponent = true;

  order = -850;

  description =
    '功能性容器，可以用来加载数据或者加载渲染器配置。加载到的数据在容器可以使用。';

  searchKeywords = '功能型容器';

  docLink = '/amis/zh-CN/components/service';

  tags = ['COC(Fish)']; // 组名称

  scaffold = {
    type: 'crm-bo-service',
    body: [],
    opt: '${options}',
    mockData,
    apiDoc,
  };

  previewSchema = {
    ...this.scaffold,
    type: 'crm-bo-service',
    body: [
      {
        type: 'tpl',
        tpl: '内容区域',
        inline: false,
        className: 'bg-light wrapper',
      },
    ],
  };

  regions: Array<RegionConfig> = [
    {
      key: 'body',
      label: '内容区',
      placeholder: amisRender({
        type: 'wrapper',
        size: 'lg',
        body: { type: 'tpl', tpl: '内容区域23' },
      }),
    },
  ];

  events: RendererPluginEvent[] = [
    {
      eventName: 'startFlowCallBack',
      eventLabel: '流程启动回调',
      description: '流程启动回调',
      defaultShow: false,
      dataSchema: (manager: EditorManager) => {
        return [
          {
            type: 'object',
            properties: {
              data: {
                type: 'object',
                title: 'Flow Data',
              },
            },
          },
        ];
      },
    },
  ];

  // actions: RendererPluginAction[] = [
  //   {
  //     actionType: 'reload',
  //     actionLabel: '重新加载',
  //     description: '触发组件数据刷新并重新渲染',
  //     ...getActionCommonProps('reload')
  //   },
  //   {
  //     actionType: 'rebuild',
  //     actionLabel: '重新构建',
  //     description: '触发schemaApi刷新，重新构建Schema',
  //     descDetail: (info: any, context: any, props: any) => {
  //       return (
  //         <div className="action-desc">
  //           重新构建
  //           {buildLinkActionDesc(props.manager, info)}
  //           Schema
  //         </div>
  //       );
  //     }
  //   },
  //   {
  //     actionType: 'setValue',
  //     actionLabel: '变量赋值',
  //     description: '更新数据域数据',
  //     ...getActionCommonProps('setValue')
  //   }
  // ];

  // dsManager: DSBuilderManager;

  // constructor(manager: EditorManager) {
  //   super(manager);
  //   this.dsManager = new DSBuilderManager(manager);
  // }

  panelBodyCreator = (context: BaseEventContext) => {
    return [
      getSchemaTpl('tabs', [
        {
          title: '属性',
          body: getSchemaTpl('collapseGroup', [
            {
              title: 'Basic',
              body: [
                // {
                //   label: 'BMF KEY',
                //   name: 'name',
                //   type: 'input-text'
                // },
                getSchemaTpl('formulaControl', {
                  label: 'Options',
                  name: 'opt',
                }),

                // getSchemaTpl('formulaControl', {
                //   label: 'BMF KEY',
                //   name: 'bmfKey',
                // }),
                // getSchemaTpl('combo-container', {
                // type: 'input-kv',
                // mode: 'normal',
                // name: 'data',
                // label: '初始化静态数据',
                // value: {
                //   bfmKey: '',
                // },
                // }),
              ],
            },
          ]),
        },
        // {
        //   title: '外观',
        //   body: [
        //     getSchemaTpl('className'),
        //     getSchemaTpl('layout:max-height', {
        //       name: 'style.height',
        //       label: '高度'
        //     })
        //   ]
        // },
        {
          title: '事件',
          className: 'p-none',
          body: getSchemaTpl('eventControl', {
            name: 'onEvent',
            ...getEventControlConfig(this.manager, context),
          }),
        },
      ]),
    ];
  };

  // panelFormPipeOut = async (schema: any, oldSchema: any) => {
  //   const entity = schema?.api?.entity;

  //   if (!entity || schema?.dsType !== ModelDSBuilderKey) {
  //     return schema;
  //   }

  //   const builder = this.dsManager.getBuilderBySchema(schema);
  //   const observedFields = ['api'];
  //   const diff = DeepDiff.diff(
  //     pick(oldSchema, observedFields),
  //     pick(schema, observedFields)
  //   );

  //   if (!diff) {
  //     return schema;
  //   }

  //   try {
  //     const updatedSchema = await builder.buildApiSchema({
  //       schema,
  //       renderer: 'service',
  //       sourceKey: 'api',
  //       apiSettings: {
  //         diffConfig: {
  //           enable: true,
  //           schemaDiff: diff
  //         }
  //       }
  //     });
  //     return updatedSchema;
  //   } catch (e) {
  //     console.error(e);
  //   }

  //   return schema;
  // };

  // patchSchema(schema: Schema) {
  //   return schema.hasOwnProperty('dsType') &&
  //     schema.dsType != null &&
  //     typeof schema.dsType === 'string'
  //     ? schema
  //     : { ...schema, dsType: ApiDSBuilderKey };
  // }

  // async buildDataSchemas(
  //   node: EditorNodeType,
  //   region?: EditorNodeType,
  //   trigger?: EditorNodeType
  // ) {
  //   let jsonschema: any = {
  //     ...jsonToJsonSchema(JSONPipeOut(node.schema.data ?? {}))
  //   };
  //   const pool = node.children.concat();

  //   while (pool.length) {
  //     const current = pool.shift() as EditorNodeType;
  //     const schema = current.schema;

  //     if (current.rendererConfig?.isFormItem && schema?.name) {
  //       const tmpSchema = await current.info.plugin.buildDataSchemas?.(
  //         current,
  //         undefined,
  //         trigger,
  //         node
  //       );
  //       jsonschema.properties[schema.name] = {
  //         ...tmpSchema,
  //         ...(tmpSchema?.$id ? {} : { $id: `${current.id}-${current.type}` })
  //       };
  //     } else if (!current.rendererConfig?.storeType) {
  //       pool.push(...current.children);
  //     }
  //   }

  //   return jsonschema;
  // }

  // rendererBeforeDispatchEvent(node: EditorNodeType, e: any, data: any) {
  //   if (e === 'fetchInited') {
  //     const scope = this.manager.dataSchema.getScope(`${node.id}-${node.type}`);
  //     const jsonschema: any = {
  //       $id: 'serviceFetchInitedData',
  //       ...jsonToJsonSchema(data.responseData)
  //     };

  //     scope?.removeSchema(jsonschema.$id);
  //     scope?.addSchema(jsonschema);
  //   }
  // }

  // async getAvailableContextFields(
  //   scopeNode: EditorNodeType,
  //   node: EditorNodeType,
  //   region?: EditorNodeType
  // ) {
  //   const builder = this.dsManager.getBuilderBySchema(scopeNode.schema);

  //   if (builder && scopeNode.schema.api) {
  //     return builder.getAvailableContextFields(
  //       {
  //         schema: scopeNode.schema,
  //         sourceKey: 'api',
  //         feat: DSFeatureEnum.List
  //       },
  //       node
  //     );
  //   }
  // }
}

registerEditorPlugin(BOServicePlugin);
