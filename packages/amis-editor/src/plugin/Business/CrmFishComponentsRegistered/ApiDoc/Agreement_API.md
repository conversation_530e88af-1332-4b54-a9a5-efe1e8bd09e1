# AgreementView API文档

## 组件说明
AgreementView组件继承自fish.View，用于处理协议相关视图，包括协议管理和设备选择功能。

## 文件路径
相对路径: ./crm-portal/src/main/webapp/crm/standardizedcomponents/pos/components/AgreementView.js

## 初始化参数
| 参数名 | 类型 | 描述 | 是否必填 |
|--------|------|------|----------|
| options.orderItem | Object | 订单项数据 | 否 |
| options.isDelivery | Boolean | 交付标志 | 否 |
| options.custOrder | Object | 客户订单信息 | 否 |
| options.showResourceType | Boolean | 资源类型显示控制 | 否 |
| options.showOldAgreement | Boolean | 旧协议显示控制 | 否 |
| options.showIccid | Boolean | ICCID显示控制 | 否 |

## 组件属性
| 属性名 | 类型 | 描述 |
|--------|------|------|
| baseTypeList | Array | SIM卡基础类型列表 |
| $handSetView | Object | 手机组件实例 |

## 核心功能模块
1. initHandSetComponent - 初始化手持设备组件，处理协议和设备选择逻辑
2. initOldAgreement - 初始化并显示旧协议信息
3. initSimCardBaseTypeSource - 初始化SIM卡基础类型数据源