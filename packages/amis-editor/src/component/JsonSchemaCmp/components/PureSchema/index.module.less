.schemaTreeContainer {
  width: 100%;
  height: 100%;
  // height: calc(100% + 3px);
  // overflow-y: auto;
  overflow-x: auto;
  padding-bottom: 6px;

  :global {
    .ant-tree-treenode {
      width: 100%;
    }
    .ant-tree-node-content-wrapper {
      flex: 1;
    }
    .ant-spin-nested-loading {
      height: 100%;
    }
    .ant-spin-container {
      height: 100%;
    }
  }
  .titleWrapper {
    display: flex;
    align-items: center;
    .title {
      color: #002b36;
      font-family: var(--theme-font-family, 'Inter Var');
    }
    .titleArray {
      color: #aeaeae;
    }
    .objectLength {
      color: #0000004d;
      margin: 0 4px;
      font-style: italic;
      min-width: 50px;
      max-width: 60px;
      display: block;
      width: fit-content;
      white-space: nowrap;
    }
  }

  .popShowRuleDetail .ant-popover-content {
    min-height: 200px;
    max-height: 400px;
    width: 700px;
    overflow: hidden;
  }

  .hasConfigRule {
    // color: @THEME_MAIN_COLOR;
    color: #ff4e16;
    // color: #2202ff;
  }
}
.previewAdvanceSettingContainer {
  width: 250px;
  max-height: 200px;
  overflow-y: auto;
  .previewAdvanceSetting {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    .ruleTypeName {
      font-weight: 500;
      font-size: 13px;
      color: #2d3040;
    }
    .ruleTypeValue {
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 12px;
      color: #575966;
    }
  }
}
