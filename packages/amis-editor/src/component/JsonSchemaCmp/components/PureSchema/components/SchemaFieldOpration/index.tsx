import { Button, Form, Input, Popover, Select } from 'antd';
import React, { useEffect, useState } from 'react';
import { SCHEMA_TYPE_MAPPING, SchemaDataType, STRING_TYPE } from '../../constants';
import { commonIsArrayItems } from '../../../../utils';

const { Option } = Select;
function SchemaFieldOpration({
  nodeData,
  operationType,
  children,
  onFormSubmit,
}: {
  nodeData: any; // todo
  operationType: 'edit' | 'add';
  children: React.ReactNode;
  onFormSubmit: (data: any) => void;
}) {
  const { type, title } = nodeData;
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);

  const INIT_FORM_VALUES = {
    fieldType: STRING_TYPE,
    fieldName: 'field_name',
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
  };

  const onSave = (values: any) => {
    onFormSubmit(values);
    setOpen(false);
  };

  useEffect(() => {
    if (open && operationType === 'edit') {
      form.setFieldsValue({
        fieldType: type,
        fieldName: title,
      });
    } else {
      form.setFieldsValue(INIT_FORM_VALUES);
    }
  }, [open]);

  return (
    <Popover
      title={operationType === 'edit' ? 'Edit Field' : 'Add Child Field'}
      trigger="click"
      open={open}
      destroyTooltipOnHide
      style={{ padding: '10px' }}
      onOpenChange={handleOpenChange}
      content={
        <Form labelCol={{ span: 8 }} wrapperCol={{ span: 16 }} form={form} name="control-hooks" onFinish={onSave}>
          <Form.Item name="fieldType" label="Field Type" rules={[{ required: true }]} style={{ marginBottom: '15px' }}>
            <Select>
              {Object.keys(SCHEMA_TYPE_MAPPING).map((typeKey) => (
                <Option key={typeKey} value={typeKey}>
                  {SCHEMA_TYPE_MAPPING[typeKey as SchemaDataType].displayName}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="fieldName" label="Field Name" rules={[{ required: true }]} style={{ marginBottom: '15px' }}>
            <Input disabled={!!(operationType === 'edit' && commonIsArrayItems(nodeData))} />
          </Form.Item>
          <Form.Item
            wrapperCol={{ span: 24 }}
            style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '10px' }}
          >
            <Button type="primary" htmlType="submit">
              Save
            </Button>
          </Form.Item>
        </Form>
      }
    >
      {children}
    </Popover>
  );
}

export default SchemaFieldOpration;
