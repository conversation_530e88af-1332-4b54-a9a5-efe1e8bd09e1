export const OBJECT_TYPE = 'object';
export const STRING_TYPE = 'string';
export const DATETIME_TYPE = 'datetime';
export const BINARY_TYPE = 'binary';
export const NUMBER_TYPE = 'number';
export const LONG_TYPE = 'long';
export const DOUBLE_TYPE = 'double';
export const FLOAT_TYPE = 'float';
export const ARRAY_TYPE = 'array';
export const BOOLEAN_TYPE = 'boolean';

export const STRING_TYPE_DEMO = 'demo string';
export const DATETIME_TYPE_DEMO = '2012-12-12 12:12:12';
export const BINARY_TYPE_DEMO = '';
export const NUMBER_TYPE_DEMO = 18888;
export const DOUBLE_TYPE_DEMO = 18.888;
export const BOOLEAN_TYPE_DEMO = true;

export type SchemaDataType =
  | typeof OBJECT_TYPE
  | typeof STRING_TYPE
  | typeof NUMBER_TYPE
  | typeof ARRAY_TYPE
  | typeof BOOLEAN_TYPE;

export const SCHEMA_TYPE_MAPPING = {
  [OBJECT_TYPE]: {
    tagColor: 'error',
    displayName: 'object',
  },
  [STRING_TYPE]: {
    tagColor: 'warning',
    displayName: 'string',
  },
  [NUMBER_TYPE]: {
    tagColor: 'success',
    displayName: 'number',
  },
  [ARRAY_TYPE]: {
    tagColor: 'processing',
    displayName: 'array',
  },
  [BOOLEAN_TYPE]: {
    tagColor: 'purple',
    displayName: 'boolean',
  },
};
