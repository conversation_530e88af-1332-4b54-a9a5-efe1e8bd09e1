import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Popconfirm, Tag, Tooltip, Tree, TreeDataNode } from 'antd';
import { union } from 'lodash';
import React, { Key, useEffect, useState } from 'react';
import { commonConvertSchemaListToTree, commonConvertTreeToSchema, commonIsArrayItems } from '../../utils';
import { ARRAY_TYPE, OBJECT_TYPE, SCHEMA_TYPE_MAPPING, SchemaDataType } from './constants';
import styles from './index.module.less';
import SchemaFieldOpration from './components/SchemaFieldOpration';

export interface IPureSchema {
  schemaList: Array<Record<string, any>>; // schema数据源
  onSchemaChange?: (schema: any) => void; // schema数据变化发生的回调
  onSelect?: (jsonPath: string, isDbClick?: boolean) => void; // tree选择事件、双击选择事件
  level: number; // 可以定义的变量层级
  showItems?: boolean;
}

export type SchemaTreeNode = TreeDataNode & {
  type: SchemaDataType;
  key: string;
  title: string;
  isRoot?: boolean;
  children: SchemaTreeNode[];
  level: number;
};

const PureSchema: React.FC<IPureSchema> = (props) => {
  const { schemaList, onSelect, onSchemaChange, level, showItems = true } = props;
  const [treeData, setTreeData] = useState<SchemaTreeNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);

  const renderDynamicTypeColor = (type: SchemaDataType) => {
    return SCHEMA_TYPE_MAPPING[type] ? (
      <Tag
        bordered={false}
        color={SCHEMA_TYPE_MAPPING[type].tagColor}
        style={{ width: '62px', textAlign: 'center', padding: '0 4px' }}
      >
        {SCHEMA_TYPE_MAPPING[type].displayName}
      </Tag>
    ) : null;
  };

  const updateNode = (
    nodes: SchemaTreeNode[],
    nodeKey: Key,
    formValues: any,
    operation: 'edit' | 'add',
  ): SchemaTreeNode[] => {
    return nodes.map((node) => {
      if (node.key === nodeKey) {
        const children = node.children || [];
        let restProps = {};

        if (operation === 'edit') {
          if (node.type === formValues.fieldType) {
            // 如果类型没变,那children不变
            restProps = { children };
          } else {
            // 如果变了,且变成array了,需要加个items
            formValues.fieldType === ARRAY_TYPE
              ? {
                children: [
                  {
                    key: `${node.key}.${formValues.fieldName}.[*]`,
                    title: 'items',
                    type: 'number',
                  },
                ],
              }
              : // 默认都加上children,转换成schema会忽略掉基础类型
              { children: [] };
          }
        } else if (operation === 'add') {
          restProps = {
            children: [
              ...children,
              {
                key: `${node.key}.${formValues.fieldName}`,
                title: formValues.fieldName,
                type: formValues.fieldType,
                // 如果新增的是array类型,需要额外增加items
                ...(formValues.fieldType === ARRAY_TYPE
                  ? {
                    children: [
                      {
                        key: `${node.key}.${formValues.fieldName}.[*]`,
                        title: 'items',
                        type: 'number',
                      },
                    ],
                  }
                  : { children: [] }),
              },
            ],
          };
        }

        return {
          ...node,
          ...(operation === 'edit'
            ? {
              title: formValues.fieldName,
              type: formValues.fieldType,
            }
            : {}),
          ...restProps,
        };
      }

      if (node.children) {
        return {
          ...node,
          children: updateNode(node.children, nodeKey, formValues, operation),
        };
      }

      return node;
    });
  };

  const handleEdit = (nodeKey: Key, formValues: any) => {
    const newTreeData = updateNode(treeData, nodeKey, formValues, 'edit');
    onSchemaChange?.(commonConvertTreeToSchema(newTreeData, showItems));
  };

  const handleAdd = (nodeKey: Key, formValues: any) => {
    const newTreeData = updateNode(treeData, nodeKey, formValues, 'add');
    setExpandedKeys(union(expandedKeys, [nodeKey]));
    onSchemaChange?.(commonConvertTreeToSchema(newTreeData, showItems));
  };

  const handleDelete = (nodeKey: Key) => {
    const removeNode = (nodes: SchemaTreeNode[]) => {
      return nodes.filter((node) => {
        if (node.children) {
          node.children = removeNode(node.children);
        }
        return node.key !== nodeKey;
      });
    };

    const newTreeData = removeNode(treeData);

    onSchemaChange?.(commonConvertTreeToSchema(newTreeData, showItems));
  };

  useEffect(() => {
    const newTreeData = schemaList.map((item) =>
      commonConvertSchemaListToTree({
        ...item,
        isRoot: true,
      }, level, showItems),
    );

    setTreeData(newTreeData);
    if (!expandedKeys.length) {
      setExpandedKeys(newTreeData.map((item) => item.key));
    }
  }, [schemaList]);

  return (
    <div className={styles.schemaTreeContainer} style={treeData?.length ? { minHeight: '60px' } : {}}>
      {treeData?.length ? (
        <Tree
          showLine
          treeData={treeData}
          onSelect={(keys) => {
            const selectPath = keys?.[0] || '';
            onSelect?.(String(selectPath));
          }}
          expandedKeys={expandedKeys}
          onExpand={(keys) => setExpandedKeys(keys)}
          titleRender={(nodeData: SchemaTreeNode) => {
            return (
              <div className={styles.titleWrapper} onDoubleClick={() => onSelect?.(nodeData.key, true)}>
                {renderDynamicTypeColor(nodeData.type)}
                <span className={styles.title}>{nodeData.title}</span>
                {nodeData.type === OBJECT_TYPE ? (
                  <span className={styles.objectLength}>{`${nodeData?.children?.length || 0} items`}</span>
                ) : null}
                <>
                  {nodeData.type === OBJECT_TYPE && (nodeData?.level < level) ? (
                    <SchemaFieldOpration
                      nodeData={nodeData}
                      operationType="add"
                      onFormSubmit={(values) => handleAdd(nodeData.key, values)}
                    >
                      <Tooltip title="Add Field">
                        <PlusOutlined style={{ marginLeft: '6px' }} />
                      </Tooltip>
                    </SchemaFieldOpration>
                  ) : null}
                  {!nodeData.isRoot ? (
                    <SchemaFieldOpration
                      nodeData={nodeData}
                      operationType="edit"
                      onFormSubmit={(values) => handleEdit(nodeData.key, values)}
                    >
                      <Tooltip title="Edit Field">
                        <EditOutlined style={{ marginLeft: '6px' }} />
                      </Tooltip>
                    </SchemaFieldOpration>
                  ) : null}
                  {!nodeData.isRoot && !commonIsArrayItems(nodeData) ? (
                    <Popconfirm
                      title="Delete the field"
                      description="Are you sure to delete this field?"
                      onConfirm={() => handleDelete(nodeData.key)}
                      okText="Yes"
                      cancelText="No"
                    >
                      <Tooltip title="Delete Field">
                        <DeleteOutlined style={{ marginLeft: '6px' }} />
                      </Tooltip>
                    </Popconfirm>
                  ) : null}
                </>
              </div>
            );
          }}
        />
      ) : null}
    </div>
  );
};

export default PureSchema;
