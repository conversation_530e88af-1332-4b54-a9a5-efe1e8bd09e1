/*
 *headerHtml: 自定义codemirror header
 *language: 语言， 初始化阶段支持json，java，python，ruby，sql，yaml，php
 ** 如果需要其他语言，添加在constants中或者通过code mirror的extensions添加
 *extensions: 扩展，支持自定义扩展, 原code mirror的属性
 */

import { Extension, ReactCodeMirrorProps, useCodeMirror } from '@uiw/react-codemirror';
import classNames from 'classnames';
import React, { forwardRef, Ref, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { espresso } from 'thememirror';
import { languages } from './constants';
import styles from './index.module.less';

 interface ICodeMirrorCmp extends ReactCodeMirrorProps {
   headerHtml?: React.ReactNode;
   language?: string;
 }

const CodeMirrorCmp = forwardRef((props: ICodeMirrorCmp, ref: Ref<any>) => {
  const { headerHtml, language, extensions = [], ...restProps } = props;
  const launguage: Extension[] = language ? (languages?.get(language) as Extension[] || []) : [];

  const [tempView, setTempView] = useState<any>();
  const [tempState, setTempState] = useState<any>();


  const editorRef = useRef<any>();

  const { view, state, setContainer } = useCodeMirror({
    container: editorRef.current,
    basicSetup: {
      lineNumbers: true,
    },
    theme: espresso,
    onMouseDown: (e) => e.stopPropagation(),
    onClick: (e) => e.stopPropagation(),
    extensions: [launguage, ...extensions],
    ...restProps,
    onChange: (value, viewUpdate) => {
      setTempView(viewUpdate?.view);
      setTempState(viewUpdate?.state);
      restProps?.onChange?.(value, viewUpdate);
    },
  });

  useImperativeHandle(ref, () => ({
    view: tempView || view,
    state: tempState || state,
  }));

  useEffect(() => {
    if (editorRef.current) {
      setContainer(editorRef.current);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editorRef.current]);

  return (
    <div
      className={classNames(restProps?.className || '', styles.codeMirrorEditor, {
        [styles.codeMirrorExistHeader]: !!headerHtml,
      })}
    >
      {headerHtml ? <div className={styles.codeMirrorPanel}>{headerHtml}</div> : null}
      <div style={{ height: '100%' }} ref={editorRef} />
    </div>
  );
});

export default CodeMirrorCmp;
