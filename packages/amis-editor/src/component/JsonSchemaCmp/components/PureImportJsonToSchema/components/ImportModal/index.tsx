import CodeMirrorCmp from '../CodeMirrorCmp';
import { Button, Modal } from 'antd';
import cn from 'classnames';
import React, { useEffect, useState } from 'react';
import styles from './index.module.less';

interface IImportModal {
  initJson?: string;
  open: boolean;
  onClose: () => void;
  onOk: (json: string) => void;
}

const ImportModal: React.FC<IImportModal> = (props) => {
  const { initJson, open, onClose, onOk } = props;

  const [tempJson, setTempJson] = useState<string>(''); // codemirror json
  const [message, setMessage] = useState<{
    error: boolean;
    errorMessage: string;
  }>({
    error: false,
    errorMessage: '',
  }); // error message

  // validate json
  const valideJsonError = (json: string, replacer: number = 2) => {
    setMessage({
      error: false,
      errorMessage: '',
    });
    try {
      if (json) {
        const obj = JSON.parse(json);
        const str = JSON.stringify(obj, null, replacer);
        setTempJson(str);
        setMessage({
          error: false,
          errorMessage: 'Checked successfully!',
        });
      }
    } catch (error) {
      if (error instanceof Error) {
        setMessage({
          error: true,
          errorMessage: error.message,
        });
      } else {
        throw error;
      }
    }
  };

  useEffect(() => {
    if (open) {
      setTempJson(initJson || '');
      valideJsonError(initJson || '');
    }
  }, [open]);

  return (
    <Modal
      title="Import JSON"
      maskClosable={false}
      open={open}
      onOk={() => {
        onOk?.(tempJson);
      }}
      onCancel={() => onClose?.()}
      okText="OK"
      width={780}
      cancelText="Cancel"
      destroyOnClose
      okButtonProps={{
        disabled: !tempJson || message.error,
      }}
    >
      <div className={styles.importJsonContainer}>
        <div className={styles.titleArea}>
          <Button type="primary" size="small" onClick={() => valideJsonError(tempJson)}>
            Format
          </Button>
          <div
            className={cn({
              [styles.successfully]: !message.error,
              [styles.error]: message.error,
            })}
          >
            {message.errorMessage}
          </div>
        </div>
        <div className={styles.codeArea}>
          <CodeMirrorCmp
            value={tempJson}
            onChange={(value: React.SetStateAction<string>) => {
              setMessage({
                error: true,
                errorMessage: '',
              });
              setTempJson(value);
            }}
            language="json"
          />
        </div>
      </div>
    </Modal>
  );
};

export default ImportModal;
