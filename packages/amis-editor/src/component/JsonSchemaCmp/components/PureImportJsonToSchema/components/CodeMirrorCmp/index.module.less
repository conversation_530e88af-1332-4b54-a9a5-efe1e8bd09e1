.codeMirrorEditor {
  height: 100%;
  border: 1px solid #dfdfdf;
  min-height: inherit;
  max-height: inherit;
  // overflow: auto;
  width: 100%;
  .codeMirrorPanel {
    width: 100%;
    padding: 8px 8px 0;
  }
  :global {
    .cm-theme {
      height: 100%;
      // overflow: auto;
      min-height: inherit;
      max-height: inherit;
    }
    .cm-editor {
      height: 100%;
      min-height: inherit;
      max-height: inherit;
      outline: none !important; // 移除codeMirror的outline显示
    }

    .cm-editor:hover {
      transition: all 200ms ease-in, border 200ms ease-in;
      border-color: #93c5fd;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      border-right-width: 1px !important;
      outline: none !important;
    }
    .cm-scroller {
      overflow: auto;
      scrollbar-color: auto;
    }
    .cm-gutters {
      border-right: none;
    }
    .cm-gutterElement {
      padding-left: 8px;
    }
  }
}
.codeMirrorExistHeader {
  :global {
    .cm-theme {
      height: calc(100% - 32px);
    }
  }
}
