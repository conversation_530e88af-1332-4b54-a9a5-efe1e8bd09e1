import { javascript } from '@codemirror/lang-javascript';
import { json } from '@codemirror/lang-json';
import { php } from '@codemirror/lang-php';
import { python } from '@codemirror/lang-python';
import { sql } from '@codemirror/lang-sql';
import { yaml } from '@codemirror/lang-yaml';
import { StreamLanguage } from '@codemirror/language';
import { java, kotlin } from '@codemirror/legacy-modes/mode/clike';
import { groovy } from '@codemirror/legacy-modes/mode/groovy';
import { lua } from '@codemirror/legacy-modes/mode/lua';
import { ruby } from '@codemirror/legacy-modes/mode/ruby';

import { Extension } from '@uiw/react-codemirror';

// TODO: 针对aviator、QLExpress目前没有合适的语言包

export const languages = new Map<string, Extension[]>()
  .set('json', [json()])
  .set('java', [StreamLanguage.define(java)])
  .set('python', [python()])
  .set('ruby', [StreamLanguage.define(ruby)])
  .set('sql', [sql()])
  .set('yaml', [yaml()])
  .set('php', [php()])
  .set('javascript', [javascript({ jsx: true, typescript: true })])
  .set('lua', [StreamLanguage.define(lua)])
  .set('groovy', [StreamLanguage.define(groovy)])
  .set('kotlin', [StreamLanguage.define(kotlin)]);
