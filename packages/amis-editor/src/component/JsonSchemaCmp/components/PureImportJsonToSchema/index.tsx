import { Button } from 'antd';
import React, { useState } from 'react';
import * as GenerateSchema from 'generate-schema';
import ImportModal from './components/ImportModal';

interface IImportJsonToSchema {
  initJson: string;
  onNewSchemaAndJsonChange: (newData: { newJson: string; newSchema: any }) => void;
}

const PureImportJsonToSchema: React.FC<IImportJsonToSchema> = (props) => {
  const { initJson, onNewSchemaAndJsonChange } = props;

  const [openImport, setOpenImport] = useState(false); // import modal open

  return (
    <>
      <Button size="small" type="primary" onClick={() => setOpenImport(true)}>
        Import Json
      </Button>
      <ImportModal
        open={openImport}
        onClose={() => setOpenImport(false)}
        onOk={(newJson) => {
          const newSchema = GenerateSchema.json('', JSON.parse(newJson));
          onNewSchemaAndJsonChange({
            newJson,
            newSchema: { ...newSchema, isRoot: true },
          });
          setOpenImport(false);
        }}
        initJson={initJson}
      />
    </>
  );
};

export default PureImportJsonToSchema;
