/*
 * @Description: jsonSchema参数定义组件
 * @Author: wang.minglei[0027012155]
 * @Date: 2025-03-04 15:19:47
 * @LastEditors: wang.minglei[0027012155]
 * @LastEditTime: 2025-04-03 17:03:44
 */
import React from 'react';
import PureSchema from './components/PureSchema';
import PureImportJsonToSchema from './components/PureImportJsonToSchema';

export type StartNodeDataType = {
  schema: any;
  importJson: string;
};

interface IJsonSchemaCmpProps {
  data: StartNodeDataType;
  onDataChange: (data: StartNodeDataType) => void;
  level?: number; // 可以定义的变量层级
  showItems?: boolean; // array类型是否展示items
  showImportJson?: boolean; // 是否展示导入框
}
const JsonSchemaCmp = (props: IJsonSchemaCmpProps) => {
  const { data, onDataChange, level, showItems = true, showImportJson = true } = props;
  const {
    properties: { body },
  } = data?.schema || { properties: {} };

  const safeLevel = Math.max(Number(level) ?? 100, 1);

  // handle schema change(header、path、query)
  const handleSchemaChange = (type: 'header' | 'path' | 'query' | 'body', newSchema: any, newImportJson: string) => {
    if (newSchema) {
      const completeSchema = {
        ...(data?.schema || {}),
        type: 'object',
        properties: {
          ...(data?.schema?.properties || {}),
          [type]: {
            ...newSchema,
            type: 'object',
            title: type,
          },
        },
      };
      onDataChange({
        schema: completeSchema,
        importJson: newImportJson,
      });
    }
  };

  return (
    <>
      {showImportJson &&
        <div style={{ marginBottom: '8px' }}>
          <PureImportJsonToSchema
            initJson={data?.importJson}
            onNewSchemaAndJsonChange={(newData: { newJson: string; newSchema: any }) => handleSchemaChange('body', newData?.newSchema, newData?.newJson)}
          />
        </div>}
      <PureSchema
        showItems={showItems}
        level={safeLevel}
        schemaList={[body || { title: 'body', type: 'object', properties: {} }]} // properties为空会导致schema组件显示不正确
        onSchemaChange={(newSchema) => {
          handleSchemaChange('body', newSchema?.[0], data?.importJson);
        }}
      />
    </>
  );
};

export default JsonSchemaCmp;
