import * as LOGICFLOW_TYPE from './components/PureSchema/constants';
import { SchemaTreeNode } from './components/PureSchema';
import { ARRAY_TYPE, OBJECT_TYPE } from './components/PureSchema/constants';

/**
 * @description: 判断当前节点是否是数组的items
 * @param {any} node title为items,且是数组中的items
 * @return {boolean}
 */
const commonIsArrayItems = (node: SchemaTreeNode) => {
  return node.title === 'items' && String(node.key).endsWith('[*]');
};

const convertSchemaToTree = (
  key: string,
  value: any,
  parentPath: string,
  curLevel: number,
  limitLevel: number,
  showItems: boolean,
  isArrayItems?: boolean
): any => {
  const isLastLevel = curLevel === limitLevel;
  const path = isArrayItems ? '[*]' : key;
  const { properties, items, ...restProps } = value;
  let otherProps;
  // 附加属性
  if (value.type === OBJECT_TYPE && properties) {
    otherProps = {
      key: `${parentPath}.${path}`,
      children: !isLastLevel ? Object.entries(properties).map(([childKey, childValue]) => {
        return convertSchemaToTree(childKey, childValue, `${parentPath}.${path}`, curLevel + 1, limitLevel, showItems);
      }) : [],
    };
  } else if (value.type === ARRAY_TYPE && items && showItems) {
    otherProps = {
      key: `${parentPath}.${path}`,
      children: !isLastLevel ? [convertSchemaToTree('items', value.items, `${parentPath}.${path}`, curLevel + 1, limitLevel, showItems, true)] : [],
    };
  } else {
    otherProps = { key: `${parentPath}.${path}` };
  }

  return {
    level: curLevel,
    ...restProps,
    title: key,
    ...(value.isRoot ? { isRoot: true } : {}),
    ...otherProps,
  };
};

/**
 * @description: 将schema数据转换为tree data结构的数据
 * @param {any} schema schema
 * @param {number} limitLevel 限制层级
 * @param {boolean} showItems 是否要展示array的items
 * @return {object[]} treeData数组
 */
const commonConvertSchemaListToTree = (schema: any, limitLevel: number, showItems: boolean) => {
  return convertSchemaToTree(schema.title, schema, '$', 0, limitLevel, showItems);
};

/**
 * @description: 将tree data数据转换为schema结构的数据
 * @param {any} treeItem
 * @param {any} showItems 是否要展示array的items
 * @return {object[]} schema
 */
const commonConvertTreeItemToSchema = (treeItem: SchemaTreeNode, showItems: boolean): any => {
  const { children, ...otherProps } = treeItem;
  if (treeItem.type === OBJECT_TYPE) {
    return {
      ...otherProps,
      properties: (children || []).reduce((acc: { [key: string]: SchemaTreeNode }, item: SchemaTreeNode) => {
        acc[item.title] = commonConvertTreeItemToSchema(item, showItems);
        return acc;
      }, {}),
    };
  }
  if (treeItem.type === ARRAY_TYPE) {
    return {
      ...otherProps,
      ...showItems ? { items: commonConvertTreeItemToSchema(children[0], showItems) } : {},
    };
  }

  return {
    ...otherProps,
  };
};

/**
 * @description: 将tree data数据转换为schema结构的数据
 * @param {any} treeList
 * @param {any} showItems 是否要展示array的items
 * @return {object[]} schema[]
 */
const commonConvertTreeToSchema = (treeList: SchemaTreeNode[], showItems: boolean) => {
  return treeList.map((tree) => commonConvertTreeItemToSchema(tree, showItems));
};

/**
 * @description:合并schema  只取rule字段将originSchema的同路径的rule字段合并到newSchema中
 * @param {any} targetSchema
 * @param {any} originSchema
 * @return {object} new terget schema
 */
// 合并schema  只取rule字段将originSchema的同路径的rule字段合并到newSchema中
const mergeSchemasWithRules = (targetSchema: any, originSchema: any): any => {
  // 如果 targetSchema 和 originSchema 都是对象类型的 schema
  if (targetSchema.type === 'object' && originSchema && originSchema.type === 'object') {
    const merged = { ...targetSchema };

    // 遍历 targetSchema 的 properties 字段
    if (targetSchema.properties) {
      merged.properties = { ...targetSchema.properties };
      for (const key in targetSchema.properties) {
        if (originSchema.properties && originSchema.properties[key]) {
          merged.properties[key] = mergeSchemasWithRules(targetSchema.properties[key], originSchema.properties[key]);
        }
      }
    }

    // 如果 originSchema 中有 rule 字段，合并 rule
    if (originSchema.rule) {
      merged.rule = originSchema.rule;
    }

    return merged;
  }
  // 如果 targetSchema 和 originSchema 都是数组类型的 schema
  if (targetSchema.type === 'array' && originSchema && originSchema.type === 'array') {
    const merged = {
      ...targetSchema,
      items: mergeSchemasWithRules(targetSchema.items, originSchema.items), // 递归合并 items 字段
    };
    if (originSchema?.rule) {
      merged.rule = originSchema.rule;
    }
    return merged;
  }
  // 如果 targetSchema 和 originSchema 类型不同但路径一致，合并 rule 字段
  if (targetSchema && originSchema && targetSchema.type !== originSchema.type) {
    const merged = { ...targetSchema }; // 保留 targetSchema 的结构
    if (originSchema.rule) {
      merged.rule = originSchema.rule; // 仅合并 originSchema 中的 rule 字段
    }
    return merged;
  }
  // 如果 targetSchema 是简单类型

  const merged = { ...targetSchema };
  // 如果 originSchema 有 rule 字段，合并 rule
  if (originSchema && originSchema.rule) {
    merged.rule = originSchema.rule;
  }
  return merged;
};

// 将一个schema转成一个demo报文
const convertSchematToDemo = (schema: any): any => {
  const { type, properties, items } = schema;
  switch (type) {
    case LOGICFLOW_TYPE.STRING_TYPE: {
      return LOGICFLOW_TYPE.STRING_TYPE_DEMO;
    }
    case LOGICFLOW_TYPE.DATETIME_TYPE: {
      return LOGICFLOW_TYPE.DATETIME_TYPE_DEMO;
    }
    case LOGICFLOW_TYPE.NUMBER_TYPE:
    case LOGICFLOW_TYPE.LONG_TYPE: {
      return LOGICFLOW_TYPE.NUMBER_TYPE_DEMO;
    }
    case LOGICFLOW_TYPE.DOUBLE_TYPE:
    case LOGICFLOW_TYPE.FLOAT_TYPE: {
      return LOGICFLOW_TYPE.DOUBLE_TYPE_DEMO;
    }
    case LOGICFLOW_TYPE.BOOLEAN_TYPE: {
      return LOGICFLOW_TYPE.BOOLEAN_TYPE_DEMO;
    }
    case LOGICFLOW_TYPE.BINARY_TYPE: {
      return LOGICFLOW_TYPE.BINARY_TYPE_DEMO;
    }
    // 对象
    case LOGICFLOW_TYPE.OBJECT_TYPE: {
      return Object.keys(properties || {}).reduce((pre, next) => {
        return {
          ...pre,
          [next]: convertSchematToDemo(properties[next]),
        };
      }, {});
    }
    // 数组
    case LOGICFLOW_TYPE.ARRAY_TYPE: {
      return [convertSchematToDemo(items)];
    }
    default:
      return null;
  }
};

export {
  commonConvertSchemaListToTree,
  commonConvertTreeToSchema,
  commonIsArrayItems,
  convertSchematToDemo,
  mergeSchemasWithRules,
};
