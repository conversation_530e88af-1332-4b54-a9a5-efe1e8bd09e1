import {FormControlProps, FormItem} from 'amis-core';
import JsonSchemaCmp from '../component/JsonSchemaCmp';
import React from 'react';
import { ConfigProvider } from 'antd';

type StartNodeDataType = {
  schema: any;
  importJson: string;
};

interface DefineInterfaceProps extends FormControlProps {
  onChange: (value: any) => void;
  value: any;
}

const DefineInterface: React.FC<DefineInterfaceProps> = ({onChange, value}) => {
  const handleDataChange = (newData: StartNodeDataType) => {
    const {schema} = newData;
    onChange({schema});
  };

  const themeMainColor = getComputedStyle(document.documentElement).getPropertyValue('--theme-main-color').trim()

  return (
    <ConfigProvider theme={{  token: { colorPrimary: themeMainColor } }}>
      <div>
        <JsonSchemaCmp
          data={value}
          onDataChange={handleDataChange}
          level={2}
          showItems={false}
          showImportJson={false}
        />
      </div>
    </ConfigProvider>
  );
};

const DefineInterfaceRenderer = FormItem({
  type: 'define-interface'
})(DefineInterface);

export default DefineInterfaceRenderer;
