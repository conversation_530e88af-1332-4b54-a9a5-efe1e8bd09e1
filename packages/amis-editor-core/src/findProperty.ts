const findProperty = (propertyName: string) => {
  try {
    let parent = window.parent as any;
    let loopCount = 0;

    // 最多循环6次，防止死循环
    while (loopCount < 6) {
      if (parent.__PHIENIX_CONFIG && parent.__PHIENIX_CONFIG[propertyName]) { 
        return parent.__PHIENIX_CONFIG[propertyName];
      }

      // 最顶层也找过了
      if (parent === window.top) {
        return null;
      }

      parent = parent.parent as any;

      if (!parent) return null;

      loopCount += 1;
    }
  } catch (error) {
    console.error('Error finding property:', error);
    return null;
  }
  return null;
};

export default findProperty;
