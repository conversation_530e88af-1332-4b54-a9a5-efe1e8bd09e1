import React, { useState } from 'react';
import AIPNG from '../../static/assets/ai-assisant.png';
import AIPanel from './Panel/AIPanel';

const AILogoPosition = (props: any) => {
  const [showAIChat, setShowAIChat] = useState(false);

  return (
    <>
    {/* AI助手悬浮按钮 */}
    <div
      id="testFind123"
      style={{
        position: 'absolute',
        right: 50,
        bottom: 50,
        width: 40,
        height: 40,
        borderRadius: '50%',
        backgroundColor: 'rgb(43, 48, 53)',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
        cursor: 'pointer',
        zIndex: 99999,
        transition: 'all 0.3s ease',
      }}
      onClick={() => setShowAIChat(true)}
      onMouseEnter={e => {
        e.currentTarget.style.transform = 'scale(1.1)';
        e.currentTarget.style.boxShadow =
          '0 0 20px rgba(43, 48, 53, 0.6), 0 4px 16px rgba(0, 0, 0, 0.25)';
      }}
      onMouseLeave={e => {
        e.currentTarget.style.transform = 'scale(1)';
        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
      }}
    >
      <img
        src={AIPNG}
        alt="AI助手"
        style={{
          width: '20px',
          height: '20px',
          position: 'absolute',
          left: '10px',
          top: '8px',
        }}
      />
    </div>
      {/* AI聊天组件 */}
      {showAIChat && <AIPanel {...props} modalOpen={showAIChat} setModalOpen={setShowAIChat} />}
    </>

  );
};

export default AILogoPosition;
