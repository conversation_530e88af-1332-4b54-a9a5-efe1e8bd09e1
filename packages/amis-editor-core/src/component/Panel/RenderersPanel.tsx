import {
  But<PERSON>,
  Html,
  PopOverContainer,
  render,
  TestIdBuilder,
  toast,
  TooltipWrapper,
  uuidv4,
} from 'amis';
import cx from 'classnames';
import { observer } from 'mobx-react';
import React, { createRef } from 'react';
import { findDOMNode } from 'react-dom';
import { Icon } from '../../icons/index';
import { EditorManager } from '../../manager';
import { SubRendererInfo } from '../../plugin';
import { deleteBlock, getBlockList } from '../../services';
import { EditorStoreType } from '../../store/editor';
import { autobind, isHasPluginIcon, noop } from '../../util';
import SearchBusinessRendererPanel from '../base/SearchBusinessRendererPanel';
import SearchCustomRendererPanel from '../base/SearchCustomRendererPanel';
import SearchRendererPanel from '../base/SearchRendererPanel';
import IconImg from './IconImg';
import { intersection as _intersection } from 'lodash';

import ApiMarkdownPreview from './ApiMarkdownPreview';
import { registerPlugin } from './utils';

type PanelProps = {
  store: EditorStoreType;
  manager: EditorManager;
  groupedRenderers: {
    [propName: string]: Array<SubRendererInfo>;
  };
  searchRendererType: string;
  className?: string;
  testIdBuilder?: TestIdBuilder;
};

type PanelStates = {
  toggleCollapseFolderStatus: boolean;
};

@observer
export default class RenderersPanel extends React.Component<
  PanelProps,
  PanelStates
> {
  state = {
    toggleCollapseFolderStatus: false, // 用于触发重新渲染组件面板的
  };

  confirmPanel = createRef<any>();

  componentDidMount(): void {
    // registerPlugin(this.props.manager.plugins);
    this.getCustomComponentList();
  }

  // 用于记录组件分类面板的折叠状态
  curCollapseFolded: {
    [propName: string]: boolean;
  } = {};

  // 暂未使用
  @autobind
  handleRegionFilterClick(e: React.MouseEvent) {
    let region = e.currentTarget.getAttribute('data-value')!;

    const { store, manager } = this.props;
    region = region === store.subRendererRegion ? '' : region;
    manager.switchToRegion(region);
  }

  @autobind
  handleDragStart(e: React.DragEvent, label: string) {
    const current = e.currentTarget;
    const id = current.getAttribute('data-id')!;
    e.dataTransfer.setData(`dnd-dom/[data-id="${id}"]`, '');

    // 将鼠标位置调整到最左侧，以免拖入的时候挡住容器信息引来混淆
    e.dataTransfer!.setDragImage(e.target as any, 0, 0);

    // 增加默认拖拽过程中元素
    e.dataTransfer!.effectAllowed = 'copy';
  }

  // 组件点选使用
  @autobind
  handleClick(e: React.MouseEvent) {
    const id = e.currentTarget.getAttribute('data-dnd-id')!;
    this.props.manager.addElem(id);
  }

  // 改变折叠状态
  @autobind
  changeCollapseFoldStatus(tagKey: string, event: any) {
    this.curCollapseFolded[tagKey] = !this.curCollapseFolded[tagKey];
    this.setState({
      toggleCollapseFolderStatus: !this.state.toggleCollapseFolderStatus,
    });
    event.preventDefault();
    event.stopPropagation();
  }

  @autobind
  async getCustomComponentList() {
    const { systemId, moduleId } = this.props.store.pageParams || {};
    const { subRenderersByOrder } = this.props.store;
    try {
      const res: any = await getBlockList(moduleId || systemId);
      const { data = [] } = res;
      const blockList = (res.data || [])
        .filter(item => item.blockId)
        .map(item => {
          const schema = item.content ? JSON.parse(item.content) : {};
          const basicPluginName = schema?._basicPluginName || 'ContainerPlugin';
          return {
            basicPluginName,
            schema: {
              ...schema,
              blockId: item.blockId,
              blockName: item.blockName
            },
            pluginName: `${basicPluginName}${uuidv4().slice(0, 4)}`,
            name: item.blockName,
            tags: item.catgName,
            description: item.blockDesc,
          };
        });
      registerPlugin(
        this.props.manager.plugins,
        blockList,
        subRenderersByOrder,
      );
    } catch (error) {
      console.error('Error fetching block list:', error);
    }
  }

  // 渲染组件api文档
  renderApi(schema: any) {
    const { apiDoc } = schema || {};
    if (!apiDoc) {
      return null;
    }
    return <ApiMarkdownPreview content={apiDoc.raw} />;
  }

  renderThumb(schema: any) {
    const manager = this.props.manager;
    const { thumb } = schema || {};

    if (thumb) {
      return <img src={thumb} style={{ maxWidth: '600px' }} />;
    }

    return schema ? (
      render(
        schema,
        {
          onAction: noop,
        },
        manager.env,
      )
    ) : (
      <p>没有预览图</p>
    );
  }

  deletePlugin = async (pluginInfo: any) => {
    // FIXME: 本地解决方案，后面换成调接口
    console.log('pluginInfo', pluginInfo);
    const blockId = pluginInfo?.scaffold?.blockId;

    const res: any = await deleteBlock(blockId);

    if (res) {
      toast.success('删除成功');
      window.postMessage(
        {
          type: 'amis-widget-unregister-event',
          editorPluginName: pluginInfo.plugin.name,
        },
        '*',
      );
    }
    // const temp = JSON.parse(localStorage.getItem('temp') || '[]');
    // const newTemp = temp.filter(
    //   (item: any) => item.name !== pluginInfo.plugin.name
    // );
    // localStorage.setItem('temp', JSON.stringify(newTemp));
  };

  render() {
    const { store, searchRendererType, className, testIdBuilder } = this.props;
    let grouped = this.props.groupedRenderers || {};
    grouped = Object.entries(grouped).reduce((acc, [key, arr]) => {
      let filteredArr;
      if (searchRendererType === 'renderer') {
        const allowDispalyList =
          store.developMode === 'professional'
            ? store.displayPlugins.professional
            : store.displayPlugins.simplified;
        filteredArr = allowDispalyList?.length
          ? arr.filter(item =>
              allowDispalyList.includes(item.plugin.constructor.name || ''),
            )
          : arr;
      } else if (searchRendererType === 'business-renderer') {
        const allowDispalyList = store.displayPlugins.business;
        filteredArr = allowDispalyList?.length
          ? arr.filter(
              item => _intersection(allowDispalyList, item?.tags || []).length,
            )
          : arr;
      } else {
        filteredArr = arr;
      }

      if (filteredArr?.length > 0) {
        acc[key] = filteredArr;
      }
      return acc;
    }, {} as { [propName: string]: Array<SubRendererInfo> });
    const keys = Object.keys(grouped);

    return (
      <div className={cx('ae-RendererList', className)}>
        {searchRendererType === 'renderer' && (
          <SearchRendererPanel store={store} />
        )}
        {searchRendererType === 'custom-renderer' && (
          <SearchCustomRendererPanel store={store} />
        )}
        {searchRendererType === 'business-renderer' && (
          <SearchBusinessRendererPanel store={store} />
        )}
        {/* <hr className="margin-top" /> */}

        {/* {node.childRegions.length ? (
          <div className="ae-RegionFilter">
            区域：
            {node.childRegions.map(region => (
              <div
                className={
                  store.subRendererRegion === region.region ? 'is-active' : ''
                }
                data-value={region.region}
                onClick={this.handleRegionFilterClick}
                key={region.region}
              >
                {region.label}
              </div>
            ))}
          </div>
        ) : null} */}

        {/*<div className="ae-RendererList-tip">
          请选择以下组件拖入「{node?.label}」中
        </div>*/}

        <div className="ae-RendererList-groupWrap hoverShowScrollBar">
          {keys.length ? (
            keys.map((tag, index) => {
              const items = grouped[tag];

              if (!items || !items.length) {
                return null;
              }

              return (
                <React.Fragment key={tag}>
                  <div
                    key={`${tag}-head`}
                    className={'ae-RendererList-head collapse-header'}
                    onClick={(event: any) => {
                      this.changeCollapseFoldStatus(tag, event);
                    }}
                  >
                    {tag}
                    <div
                      className={cx('expander-icon', {
                        'is-folded': !!this.curCollapseFolded[tag],
                      })}
                      title={
                        this.curCollapseFolded[tag] ? '点击展开' : '点击折叠'
                      }
                    >
                      <Icon icon="tree-down" />
                    </div>
                  </div>
                  <div
                    key={`${tag}-content`}
                    className={cx('ae-RendererList-group collapse-content', {
                      'is-folded': !!this.curCollapseFolded[tag],
                    })}
                  >
                    {items
                      .sort((a, b) => a.name.localeCompare(b.name))
                      .map((item: any) => {
                        const key = `${index}_${item.id}`;
                        const usePluginIcon = isHasPluginIcon(item);
                        const testid = `editor-renderer-${item.plugin.rendererName}`;
                        const deletable = item?.plugin?.deletable;

                        let iconName;
                        if (searchRendererType === 'business-renderer') {
                          iconName = 'Business Component';
                        } else if (searchRendererType === 'custom-renderer') {
                          iconName =
                            item.plugin.pluginIcon || 'Custom Component';
                        } else if (searchRendererType === 'renderer') {
                          iconName = item.pluginIcon;
                        }

                        return (
                          <div
                            key={key}
                            className="ae-RendererList-item"
                            draggable
                            data-id={key}
                            data-dnd-type="subrenderer"
                            data-dnd-id={item.id}
                            data-dnd-data={JSON.stringify(
                              item.scaffold || { type: item.type },
                            )}
                            onDragStart={(e: React.DragEvent) =>
                              this.handleDragStart(e, item.name)
                            }
                            {...testIdBuilder?.getChild(testid).getTestId()}
                          >
                            {/* <div
                            className="icon-box"
                            data-dnd-id={item.id}
                            title={`点击添加「${item.name}」`}
                            onClick={this.handleClick}
                          >
                            {usePluginIcon && <Icon icon={item.pluginIcon} />}
                            {!usePluginIcon && (
                              <i
                                className={cx(
                                  'fa-fw',
                                  item.icon || 'fa fa-circle-thin'
                                )}
                              />
                            )}
                          </div> */}
                            <div
                              className="ae-img-box"
                              data-dnd-id={item.id}
                              title={`点击添加「${item.name}」`}
                              onClick={this.handleClick}
                            >
                              <IconImg className="img" iconName={iconName} />
                              <div className="ae-img-hover"></div>
                              <div onClick={e => e.stopPropagation()}>
                                {deletable && (
                                  <PopOverContainer
                                    popOverContainer={() => findDOMNode(this)}
                                    popOverRender={({ onClose }) => (
                                      <div className="ae-DeleteconfirmPanel">
                                        <div>确定删除此插件吗？</div>
                                        <div className="ae-DeleteconfirmBtn">
                                          <Button
                                            size="xs"
                                            onClick={() => {
                                              onClose();
                                            }}
                                          >
                                            取消
                                          </Button>
                                          <Button
                                            size="xs"
                                            level="primary"
                                            onClick={() => {
                                              onClose();
                                              this.deletePlugin(item);
                                            }}
                                          >
                                            确定
                                          </Button>
                                        </div>
                                      </div>
                                    )}
                                  >
                                    {({ onClick, ref }) => (
                                      <div
                                        ref={ref}
                                        className="ae-RendererDelete"
                                        onClick={e => {
                                          onClick(e);
                                        }}
                                      >
                                        <Icon
                                          icon="delete-easy-btn"
                                          className="icon"
                                        />
                                      </div>
                                    )}
                                  </PopOverContainer>
                                )}
                                <TooltipWrapper
                                  tooltipClassName="ae-RendererThumb"
                                  trigger="hover"
                                  rootClose={true}
                                  placement="auto"
                                  tooltip={{
                                    offset: [10, -10], // x轴偏移，避免遮挡边框
                                    children: () => (
                                      <div>
                                        <div className="ae-Renderer-title">
                                          {item.name}
                                        </div>

                                        {item.description || item.docLink ? (
                                          <div className="ae-Renderer-info">
                                            <Html
                                              html={
                                                item.description
                                                  ? item.description
                                                  : ''
                                              }
                                            />
                                            {/* {item.docLink && (
                                          <a
                                            target="_blank"
                                            href={
                                              store.amisDocHost + item.docLink
                                            }
                                          >
                                            详情
                                          </a>
                                        )} */}
                                          </div>
                                        ) : null}

                                        <div className="ae-Renderer-preview">
                                          {this.renderThumb(item.previewSchema)}
                                        </div>
                                        <div
                                          className={cx({
                                            'ae-api-preview':
                                              item?.previewSchema?.apiDoc,
                                          })}
                                        >
                                          {this.renderApi(item.previewSchema)}
                                        </div>
                                      </div>
                                    ),
                                  }}
                                >
                                  <div className="ae-RendererIcon">
                                    <Icon icon="editor-help" className="icon" />
                                  </div>
                                </TooltipWrapper>
                              </div>
                            </div>
                            <div className="ae-name-box">
                              <div
                                className="ae-RendererInfo"
                                data-dnd-id={item.id}
                                title={item?.name}
                              >
                                {item.name}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </React.Fragment>
              );
            })
          ) : (
            <span>没有找到可用组件，您可以换个关键字继续查找。</span>
          )}
        </div>
      </div>
    );
  }
}
