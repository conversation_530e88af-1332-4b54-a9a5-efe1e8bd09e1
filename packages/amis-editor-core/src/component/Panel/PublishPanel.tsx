import {observer} from 'mobx-react';
import React from 'react';
import {Modal, Form, Input, FormInstance, Select} from 'antd';
import {Icon, toast, uuid, uuidv4} from 'amis';
import {EditorStoreType} from '../../store/editor';
import {registerPlugin} from './utils';
import {EditorManager} from '../../manager';
import {EditorNodeType} from '../../store/node';
import {guid, JSONPipeOut, reGenerateID} from '../../util';
import {createBlock, getBlockCatalogList} from '../../services';
import NewModal from '../../component/cdp/modal';

export interface PanelsProps {
  store: EditorStoreType;
  manager: EditorManager;
  node: EditorNodeType | undefined;
  theme?: string;
}

const icons = [
  'address-book',
  'address-book-o',
  'address-card',
  'address-card-o',
  'bandcamp',
  'bath',
  'id-card',
  'id-card-o',
  'eercast',
  'envelope-open',
  'envelope-open-o',
  'etsy',
  'free-code-camp',
  'grav',
  'handshake-o',
  'id-badge',
  'imdb',
  'linode',
  'meetup',
  'microchip',
  'podcast',
  'quora',
  'ravelry',
  'shower',
  'snowflake-o',
  'superpowers',
  'telegram',
  'thermometer-full',
  'thermometer-empty',
  'thermometer-quarter',
  'thermometer-half',
  'thermometer-three-quarters',
  'window-close',
  'window-close-o',
  'user-circle',
  'user-circle-o',
  'user-o',
  'window-maximize',
  'window-minimize',
  'window-restore',
  'wpexplorer',
  'adjust',
  'american-sign-language-interpreting',
  'anchor',
  'archive',
  'area-chart',
  'arrows',
  'arrows-h',
  'arrows-v',
  'assistive-listening-systems',
  'asterisk',
  'at',
  'audio-description',
  'car',
  'balance-scale',
  'ban',
  'university',
  'bar-chart',
  'barcode',
  'bars',
  'battery-full',
  'battery-empty',
  'battery-quarter',
  'battery-half',
  'battery-three-quarters',
  'bed',
  'beer',
  'bell',
  'bell-o',
  'bell-slash',
  'bell-slash-o',
  'bicycle',
  'binoculars',
  'birthday-cake',
  'blind',
  'bluetooth',
  'bluetooth-b',
  'bolt',
  'bomb',
  'book',
  'bookmark',
  'bookmark-o',
  'braille',
  'briefcase',
  'bug',
  'building',
  'building-o',
  'bullhorn',
  'bullseye',
  'bus',
  'taxi',
  'calculator',
  'calendar',
  'calendar-check-o',
  'calendar-minus-o',
  'calendar-o',
  'calendar-plus-o',
  'calendar-times-o',
  'camera',
  'camera-retro',
  'caret-square-o-down',
  'caret-square-o-left',
  'caret-square-o-right',
  'caret-square-o-up',
  'cart-arrow-down',
  'cart-plus',
  'cc',
  'certificate',
  'check',
  'check-circle',
  'check-circle-o',
  'check-square',
  'check-square-o',
  'child',
  'circle',
  'circle-o',
  'circle-o-notch',
  'circle-thin',
  'clock-o',
  'clone',
  'times',
  'cloud',
  'cloud-download',
  'cloud-upload',
  'code',
  'code-fork',
  'coffee',
  'cog',
  'cogs',
  'comment',
  'comment-o',
  'commenting',
  'commenting-o',
  'comments',
  'comments-o',
  'compass',
  'copyright',
  'creative-commons',
  'credit-card',
  'credit-card-alt',
  'crop',
  'crosshairs',
  'cube',
  'cubes',
  'cutlery',
  'tachometer',
  'database',
  'deaf',
  'desktop',
  'diamond',
  'dot-circle-o',
  'download',
  'pencil-square-o',
  'ellipsis-h',
  'ellipsis-v',
  'envelope',
  'envelope-o',
  'envelope-square',
  'eraser',
  'exchange',
  'exclamation',
  'exclamation-circle',
  'exclamation-triangle',
  'external-link',
  'external-link-square',
  'eye',
  'eye-slash',
  'eyedropper',
  'fax',
  'rss',
  'female',
  'fighter-jet',
  'file-archive-o',
  'file-audio-o',
  'file-code-o',
  'file-excel-o',
  'file-image-o',
  'file-video-o',
  'file-pdf-o',
  'file-powerpoint-o',
  'file-word-o',
  'film',
  'filter',
  'fire',
  'fire-extinguisher',
  'flag',
  'flag-checkered',
  'flag-o',
  'flask',
  'folder',
  'folder-o',
  'folder-open',
  'folder-open-o',
  'frown-o',
  'futbol-o',
  'gamepad',
  'gavel',
  'gift',
  'glass',
  'globe',
  'graduation-cap',
  'users',
  'hand-rock-o',
  'hand-lizard-o',
  'hand-paper-o',
  'hand-peace-o',
  'hand-pointer-o',
  'hand-scissors-o',
  'hand-spock-o',
  'hashtag',
  'hdd-o',
  'headphones',
  'heart',
  'heart-o',
  'heartbeat',
  'history',
  'home',
  'hourglass',
  'hourglass-start',
  'hourglass-half',
  'hourglass-end',
  'hourglass-o',
  'i-cursor',
  'picture-o',
  'inbox',
  'industry',
  'info',
  'info-circle',
  'key',
  'keyboard-o',
  'language',
  'laptop',
  'leaf',
  'lemon-o',
  'level-down',
  'level-up',
  'life-ring',
  'lightbulb-o',
  'line-chart',
  'location-arrow',
  'lock',
  'low-vision',
  'magic',
  'magnet',
  'share',
  'reply',
  'reply-all',
  'male',
  'map',
  'map-marker',
  'map-o',
  'map-pin',
  'map-signs',
  'meh-o',
  'microphone',
  'microphone-slash',
  'minus',
  'minus-circle',
  'minus-square',
  'minus-square-o',
  'mobile',
  'money',
  'moon-o',
  'motorcycle',
  'mouse-pointer',
  'music',
  'newspaper-o',
  'object-group',
  'object-ungroup',
  'paint-brush',
  'paper-plane',
  'paper-plane-o',
  'paw',
  'pencil',
  'pencil-square',
  'percent',
  'phone',
  'phone-square',
  'pie-chart',
  'plane',
  'plug',
  'plus',
  'plus-circle',
  'plus-square',
  'plus-square-o',
  'power-off',
  'print',
  'puzzle-piece',
  'qrcode',
  'question',
  'question-circle',
  'question-circle-o',
  'quote-left',
  'quote-right',
  'random',
  'recycle',
  'refresh',
  'registered',
  'retweet',
  'road',
  'rocket',
  'rss-square',
  'search',
  'search-minus',
  'search-plus',
  'server',
  'share-alt',
  'share-alt-square',
  'share-square',
  'share-square-o',
  'shield',
  'ship',
  'shopping-bag',
  'shopping-basket',
  'shopping-cart',
  'sign-in',
  'sign-language',
  'sign-out',
  'signal',
  'sitemap',
  'sliders',
  'smile-o',
  'sort',
  'sort-alpha-asc',
  'sort-alpha-desc',
  'sort-amount-asc',
  'sort-amount-desc',
  'sort-asc',
  'sort-desc',
  'sort-numeric-asc',
  'sort-numeric-desc',
  'space-shuttle',
  'spinner',
  'spoon',
  'square',
  'square-o',
  'star',
  'star-half',
  'star-half-o',
  'star-o',
  'sticky-note',
  'sticky-note-o',
  'street-view',
  'suitcase',
  'sun-o',
  'tablet',
  'tag',
  'tags',
  'tasks',
  'television',
  'terminal',
  'thumb-tack',
  'thumbs-down',
  'thumbs-o-down',
  'thumbs-o-up',
  'thumbs-up',
  'ticket',
  'times-circle',
  'times-circle-o',
  'tint',
  'toggle-off',
  'toggle-on',
  'trademark',
  'trash',
  'trash-o',
  'tree',
  'trophy',
  'truck',
  'tty',
  'umbrella',
  'universal-access',
  'unlock',
  'unlock-alt',
  'upload',
  'user',
  'user-plus',
  'user-secret',
  'user-times',
  'video-camera',
  'volume-control-phone',
  'volume-down',
  'volume-off',
  'volume-up',
  'wheelchair',
  'wheelchair-alt',
  'wifi',
  'wrench',
  'hand-o-down',
  'hand-o-left',
  'hand-o-right',
  'hand-o-up',
  'ambulance',
  'subway',
  'train',
  'genderless',
  'transgender',
  'mars',
  'mars-double',
  'mars-stroke',
  'mars-stroke-h',
  'mars-stroke-v',
  'mercury',
  'neuter',
  'transgender-alt',
  'venus',
  'venus-double',
  'venus-mars',
  'file',
  'file-o',
  'file-text',
  'file-text-o',
  'cc-amex',
  'cc-diners-club',
  'cc-discover',
  'cc-jcb',
  'cc-mastercard',
  'cc-paypal',
  'cc-stripe',
  'cc-visa',
  'google-wallet',
  'paypal',
  'btc',
  'jpy',
  'usd',
  'eur',
  'gbp',
  'gg',
  'gg-circle',
  'ils',
  'inr',
  'krw',
  'rub',
  'try',
  'viacoin',
  'align-center',
  'align-justify',
  'align-left',
  'align-right',
  'bold',
  'link',
  'chain-broken',
  'clipboard',
  'columns',
  'files-o',
  'scissors',
  'outdent',
  'floppy-o',
  'font',
  'header',
  'indent',
  'italic',
  'list',
  'list-alt',
  'list-ol',
  'list-ul',
  'paperclip',
  'paragraph',
  'repeat',
  'undo',
  'strikethrough',
  'subscript',
  'superscript',
  'table',
  'text-height',
  'text-width',
  'th',
  'th-large',
  'th-list',
  'underline',
  'angle-double-down',
  'angle-double-left',
  'angle-double-right',
  'angle-double-up',
  'angle-down',
  'angle-left',
  'angle-right',
  'angle-up',
  'arrow-circle-down',
  'arrow-circle-left',
  'arrow-circle-o-down',
  'arrow-circle-o-left',
  'arrow-circle-o-right',
  'arrow-circle-o-up',
  'arrow-circle-right',
  'arrow-circle-up',
  'arrow-down',
  'arrow-left',
  'arrow-right',
  'arrow-up',
  'arrows-alt',
  'caret-down',
  'caret-left',
  'caret-right',
  'caret-up',
  'chevron-circle-down',
  'chevron-circle-left',
  'chevron-circle-right',
  'chevron-circle-up',
  'chevron-down',
  'chevron-left',
  'chevron-right',
  'chevron-up',
  'long-arrow-down',
  'long-arrow-left',
  'long-arrow-right',
  'long-arrow-up',
  'backward',
  'compress',
  'eject',
  'expand',
  'fast-backward',
  'fast-forward',
  'forward',
  'pause',
  'pause-circle',
  'pause-circle-o',
  'play',
  'play-circle',
  'play-circle-o',
  'step-backward',
  'step-forward',
  'stop',
  'stop-circle',
  'stop-circle-o',
  'youtube-play',
  '500px',
  'adn',
  'amazon',
  'android',
  'angellist',
  'apple',
  'behance',
  'behance-square',
  'bitbucket',
  'bitbucket-square',
  'black-tie',
  'buysellads',
  'chrome',
  'codepen',
  'codiepie',
  'connectdevelop',
  'contao',
  'css3',
  'dashcube',
  'delicious',
  'deviantart',
  'digg',
  'dribbble',
  'dropbox',
  'drupal',
  'edge',
  'empire',
  'envira',
  'expeditedssl',
  'font-awesome',
  'facebook',
  'facebook-official',
  'facebook-square',
  'firefox',
  'first-order',
  'flickr',
  'fonticons',
  'fort-awesome',
  'forumbee',
  'foursquare',
  'get-pocket',
  'git',
  'git-square',
  'github',
  'github-alt',
  'github-square',
  'gitlab',
  'gratipay',
  'glide',
  'glide-g',
  'google',
  'google-plus',
  'google-plus-official',
  'google-plus-square',
  'hacker-news',
  'houzz',
  'html5',
  'instagram',
  'internet-explorer',
  'ioxhost',
  'joomla',
  'jsfiddle',
  'lastfm',
  'lastfm-square',
  'leanpub',
  'linkedin',
  'linkedin-square',
  'linux',
  'maxcdn',
  'meanpath',
  'medium',
  'mixcloud',
  'modx',
  'odnoklassniki',
  'odnoklassniki-square',
  'opencart',
  'openid',
  'opera',
  'optin-monster',
  'pagelines',
  'pied-piper',
  'pied-piper-alt',
  'pied-piper-pp',
  'pinterest',
  'pinterest-p',
  'pinterest-square',
  'product-hunt',
  'qq',
  'rebel',
  'reddit',
  'reddit-alien',
  'reddit-square',
  'renren',
  'safari',
  'scribd',
  'sellsy',
  'shirtsinbulk',
  'simplybuilt',
  'skyatlas',
  'skype',
  'slack',
  'slideshare',
  'snapchat',
  'snapchat-ghost',
  'snapchat-square',
  'soundcloud',
  'spotify',
  'stack-exchange',
  'stack-overflow',
  'steam',
  'steam-square',
  'stumbleupon',
  'stumbleupon-circle',
  'tencent-weibo',
  'themeisle',
  'trello',
  'tripadvisor',
  'tumblr',
  'tumblr-square',
  'twitch',
  'twitter',
  'twitter-square',
  'usb',
  'viadeo',
  'viadeo-square',
  'vimeo',
  'vimeo-square',
  'vine',
  'vk',
  'weixin',
  'weibo',
  'whatsapp',
  'wikipedia-w',
  'windows',
  'wordpress',
  'wpbeginner',
  'wpforms',
  'xing',
  'xing-square',
  'y-combinator',
  'yahoo',
  'yelp',
  'yoast',
  'youtube',
  'youtube-square',
  'h-square',
  'hospital-o',
  'medkit',
  'stethoscope',
  'user-md'
];

@observer
export class PublishPanel extends React.Component<PanelsProps> {
  formRef = React.createRef<FormInstance>();

  state = {
    iconCount: 100,
    catalogOptions: []
  };

  filterHiddenProps(key: string) {
    return key === '$$id' || key.substring(0, 2) === '__';
  }

  nameValidator = (_: any, value: any) => {
    if (!value) return Promise.resolve();

    const {manager} = this.props;
    const {plugins} = manager;

    const found = plugins.find((item: any) => item.name === value);

    return found ? Promise.reject('name already exists') : Promise.resolve();
  };

  getCatalogList = async () => {
    const {projectId, moduleId} = this.props.store.pageParams || {};
    const res = await getBlockCatalogList(projectId, moduleId);
    const options = (res.data || [])
      .filter(item => !item.blockId)
      .map(item => {
        return {
          label: item.catgName,
          value: item.catgId
        };
      });
    this.setState({
      catalogOptions: options
    });
  };

  publish = () => {
    const {manager, store, node} = this.props;
    const {moduleId} = this.props.store.pageParams || {};
    const {plugins} = manager;
    const {subRenderersByOrder} = store;
    const basicPluginName = node?.info.plugin.constructor.name;
    this.formRef.current
      ?.validateFields()
      .then((value: any) => {
        const schema = node?.schema;
        const newSchema = reGenerateID(schema);
        const blockKey = `blockKey-${guid()}`;
        newSchema._basicPluginName = basicPluginName;

        createBlock({
          ...value,
          moduleId,
          content: JSON.stringify({
            ...JSONPipeOut(newSchema, this.filterHiddenProps),
            overrideBaseSchema: true,
            blockKey 
          })
        }).then(res => {
          if (res?.blockId) {
            toast.success('新建成功');
            const tags = this.state.catalogOptions.find(
              item => item.value === value.catgId
            )?.label;
            const data = {
              name: value.blockName,
              description: value.blockDesc,
              tags,
              basicPluginName,
              pluginName: `${basicPluginName}${uuidv4().slice(0, 4)}`,
              schema: {
                ...JSONPipeOut(newSchema, this.filterHiddenProps),
                overrideBaseSchema: true,
                blockKey
              }
            };
            registerPlugin(plugins, [data], subRenderersByOrder);
            this.closePublishPanel();
          }
        });
      })
      .catch(() => {});
  };

  closePublishPanel = () => {
    this.formRef.current?.resetFields();
    const {store} = this.props;
    store.closePublishPanel();
  };

  componentDidMount() {
    this.getCatalogList();
  }

  render(): React.ReactNode {
    const {store} = this.props;
    const {iconCount, catalogOptions} = this.state;
    const showPublishRenderer = store.showPublishRenderer;
    return (
      <NewModal
        title="发布组件"
        open={showPublishRenderer}
        onCancel={this.closePublishPanel}
        onClose={this.closePublishPanel}
        onOk={this.publish}
      >
        <Form labelCol={{span: 6}} wrapperCol={{span: 18}} ref={this.formRef}>
          <Form.Item
            required
            rules={[
              {required: true, message: '名称必填'},
              {validator: this.nameValidator}
            ]}
            name="blockName"
            label="名称"
          >
            <Input />
          </Form.Item>

          {/* <Form.Item name="icon" label="图标">
            <Select
              allowClear
              virtual={false}
              popupClassName="publish-panel-icon"
              labelRender={value => {
                return <i className={value.label as string} />;
              }}
              options={icons.slice(0, iconCount).map(icon => ({
                label: `fa fa-${icon}`,
                value: `fa fa-${icon}`
              }))}
              optionRender={option => {
                return <i className={option.data.value} />;
              }}
              onPopupScroll={event => {
                const target = event.target as HTMLElement;
                if (
                  target.scrollTop + target.clientHeight + 10 >
                  target.scrollHeight
                ) {
                  if (iconCount < icons.length) {
                    this.setState({
                      iconCount: Math.min(iconCount + 100, icons.length)
                    });
                  }
                }
              }}
            />
          </Form.Item> */}
          <Form.Item
            name="catgId"
            label="目录"
            rules={[{required: true, message: '目录必选'}]}
          >
            <Select options={catalogOptions} />
          </Form.Item>
          <Form.Item
            required
            name="blockDesc"
            label="描述"
          >
            <Input />
          </Form.Item>
        </Form>
      </NewModal>
    );
  }
}
