import { PauseCircleOutlined, SendOutlined } from '@ant-design/icons';
import { render } from 'amis';
import {
  Button,
  Flex,
  Form,
  FormInstance,
  Input,
  InputRef,
  Modal,
  Spin,
} from 'antd';
import cls from 'classnames';
import { observer } from 'mobx-react';
import OpenAI, { APIUserAbortError } from 'openai';
import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { darcula } from 'react-syntax-highlighter/dist/esm/styles/prism';
import AiPageGenerate from './AIPanelProChat';
import { ProChatProvider } from '@ant-design/pro-chat';
import styles from './AIPanelProChat/index.module.scss';

const openai = new OpenAI({
  baseURL: 'https://api.deepseek.com',
  apiKey: '***********************************',
  dangerouslyAllowBrowser: true,
});

enum ConversionState {
  idel = 'idel',
  waiting = 'waiting',
  processing = 'processing',
  success = 'success',
  error = 'error',
}

// 只修改背景色
const customStyle = {
  ...darcula,
  'code[class*="language-"]': {
    ...darcula['code[class*="language-"]'],
    backgroundColor: '#1e1e1e', // 修改代码背景色
  },
  'pre[class*="language-"]': {
    ...darcula['pre[class*="language-"]'],
    backgroundColor: '#1e1e1e', // 修改代码块的背景色
  },
};

// @observer
class AIPanel extends React.Component<any, any> {
  inputRef = React.createRef<InputRef>();
  divRef = React.createRef<HTMLDivElement>();
  formRef = React.createRef<FormInstance>();
  completion: any;
  controller: AbortController | null;
  timer: any = null;
  isComposing: boolean;

  state = {
    conversionState: ConversionState.idel,
    previewList: [],
    value: undefined,
  };

  componentDidMount(): void {
    document.addEventListener('compositionstart', this.onCompositionstart);
    document.addEventListener('compositionend', this.onCompositionEnd);
  }

  onCompositionstart = () => {
    this.isComposing = true;
  };

  onCompositionEnd = () => {
    this.isComposing = false;
  };

  componentWillUnmount(): void {
    document.removeEventListener('compositionstart', this.onCompositionstart);
    document.removeEventListener('compositionend', this.onCompositionEnd);
  }

  scrollToBottomInTimer = () => {
    this.timer = setInterval(() => {
      const scrollableDiv = this.divRef.current as HTMLDivElement;
      const scrollHeight = scrollableDiv.scrollHeight;
      const scrollTop = scrollableDiv.scrollTop;
      const clientHeight = scrollableDiv.clientHeight;
      const distance = scrollHeight - scrollTop - clientHeight;
      if (distance > 2) {
        scrollableDiv.scrollTo({
          top: scrollableDiv.scrollHeight,
          behavior: 'smooth',
        });
      }
    }, 50);
  };

  send = async () => {
    if (
      this.state.conversionState !== ConversionState.idel ||
      !this.state.value
    )
      return;

    const value = this.formRef.current?.getFieldValue('input');
    const { addConversations, updateLastConversation } = this.props.store;
    this.setState({
      conversionState: ConversionState.waiting,
    });

    const { conversations = [] } = this.props.store;
    try {
      this.formRef.current?.resetFields();

      this.controller = new AbortController();
      const curLanguage = localStorage.getItem('suda-i18n-locale') || 'zh-CN';

      const messages = [
        {
          role: 'system',
          content:
            '你是一个对百度amis精通的专家，你的身份是page-designer，问题都是amis相关的，根据这个设定回答问题，答案中不要提及百度和amis', // 设定系统 Prompt
        },
        {
          role: 'system',
          content:
            '返回的amis的json中不要增加注释，解释可以保留， 如果没有明确要返回page，就不需要返回page类型',
        },
        {
          role: 'system',
          content:
            '表单中有个body字段存放表单项，其中表单中文本输入类型是input-text, 邮箱类型是input-email, 数字输入类型是input-number, 日期选择是input-date, 时间选择是input-time, 日期时间选择是input-datetime, 富文本是textarea, 单选是radios, 尽量使用select少用radios',
        },
        {
          role: 'system',
          content:
            '表单类型是easy-form, 不需要actions字段, 表单上增加 wrapWithPanel: false,labelAlign: right, mode: flex, rolCount:1, labelWidth: 150px, className: overflow-ellipsis easy-form-gap 属性',
        },
        {
          role: 'system',
          content:
            '增加一个表单项，表单项的label是" "，类型是button-toolbar,className属性是"easy-form-button-toolbar"，buttons属性是个数组，数组中有两个元素，第一个元素是一个对象，type是submit, label是"提交", level是"primary", 另一个元素是一个对象，type是reset, label是"重置"',
        },
        {
          role: 'system',
          content:
            '每个表单项增加colNumber: 1, colSize: calc((100% - 0px) * 1.00000 * 1 + 0px)属性',
        },
        {
          role: 'system',
          content: '每个表单项增加一个row属性，row从0开始递增',
        },
        {
          role: 'system',
          content:
            curLanguage === 'zh-CN'
              ? '请使用中文回答'
              : 'Please answer the question in English',
        },
        ...conversations,
        { role: 'system', content: `${value}` },
      ];

      addConversations({
        role: 'user',
        content: value,
      });

      this.scrollToBottomInTimer();

      // const stream = await openai.chat.completions.create(
      //   {
      //     messages,
      //     model: 'deepseek-chat',
      //     stream: true // 启用流式响应
      //   },
      //   {
      //     signal: this.controller.signal
      //   }
      // );

      const response = await fetch(
        'https://lab.iwhalecloud.com/gpt-proxy/v1/chat/completions',
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${import.meta.env.VITE_GPT_TOKEN}`,
            'content-type': 'application/json',
          },
          body: JSON.stringify({
            model: 'deepseek-ai/DeepSeek-V3',
            stream: true,
            messages,
          }),
        },
      );

      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let buffer = '';
      let addFlag = true;

      while (true) {
        const { value, done } = await reader.read();
        if (done) {
          break;
        }
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        const lines = buffer.split('\n');
        buffer = lines.pop();

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const message = line.slice(6);
            if (message === '[DONE]\r' || message === '[DONE]') break; // 终止标志 https://docs.iwhalecloud.com/doi/cS6oKY/gpt-proxy-desc/gpt-proxy?highlight=done
            let content;
            try {
              const chunk = JSON.parse(message);
              content = chunk.choices[0]?.delta?.content;
            } catch (error) {
              console.error('Error parsing JSON:', error);
            }
            if (addFlag && content) {
              addConversations({
                role: 'assistant',
                content: content,
              });
              addFlag = false;
              this.setState({
                conversionState: ConversionState.processing,
              });
            } else {
              if (content) {
                updateLastConversation(content);
              }
            }
          }
        }
      }

      // 防止stream为空的情况
      if (addFlag) {
        addConversations({
          role: 'assistant',
          content: '没有返回结果',
        });
      }
    } catch (error) {
      if (error instanceof APIUserAbortError) {
        addConversations({
          role: 'assistant',
          content: '已取消',
        });
      } else {
        addConversations({
          role: 'assistant',
          content: '抱歉，暂时无法回答这个问题',
        });
      }
    } finally {
      this.setState({
        conversionState: ConversionState.idel,
        value: undefined,
      });
      clearInterval(this.timer);
      this.timer = null;
    }
  };

  cancel = async () => {
    this.formRef.current?.setFieldsValue({
      input: '',
    });
    if (!this.controller?.signal.aborted) {
      this.controller?.abort();
      this.controller = null;
    }
    this.setState({
      conversionState: ConversionState.idel,
    });
  };

  insertSchemaNew = (schema: string) => {
    const { store, manager } = this.props;
    const { changeLeftPanelKey } = store;
    changeLeftPanelKey?.('renderers');
    this.props?.setModalOpen(false);
    // FIXME: 校验插入逻辑是否合理
    // FIXME：schema的转换单独封装成函数
    const validSchema = schema
      .trim();
    try {
      const schema = JSON.parse(validSchema);
      manager.addElem(schema);
    } catch (error) {
    } finally {
    }
  };


  insertSchema = (schema: string) => {
    const { store, manager } = this.props;
    const { changeLeftPanelKey } = store;
    changeLeftPanelKey('renderers');
    // FIXME: 校验插入逻辑是否合理
    // FIXME：schema的转换单独封装成函数
    const validSchema = schema
      .replace(/^```json/, '')
      .replace(/```$/, '')
      .trim();
    try {
      const schema = JSON.parse(validSchema);
      manager.addElem(schema);
    } catch (error) {
    } finally {
    }
  };

  renderRes = (content: string, group: number, isLast: boolean) => {
    if (!content) return content;
    const { conversionState } = this.state;
    const disabled =
      isLast &&
      (conversionState === ConversionState.waiting ||
        conversionState === ConversionState.processing);

    const contents = content.split(/(```json[\s\S]*```)/);
    if (
      /```json/.test(contents[contents.length - 1]) &&
      !/^```json/.test(contents[contents.length - 1])
    ) {
      const cs = contents[contents.length - 1]
        .split(/(```json[\s\S]*)/)
        .filter(item => !!item);
      contents.splice(contents.length - 1, 1, ...cs);
    }
    return contents.map((item: string, index: number) => {
      if (/^```json/.test(item)) {
        const isPrevew = this.state.previewList.includes(`${group}-${index}`);
        return (
          <div>
            <div className="toolbar">
              <Button
                disabled={disabled}
                type="text"
                onClick={() => this.insertSchema(item)}
              >
                插入
              </Button>
              <Button
                disabled={disabled}
                type="text"
                onClick={() => {
                  if (isPrevew) {
                    this.setState({
                      previewList: this.state.previewList.filter(
                        (i: number) => i !== `${group}-${index}`,
                      ),
                    });
                  } else {
                    this.setState({
                      previewList: [
                        ...this.state.previewList,
                        `${group}-${index}`,
                      ],
                    });
                  }
                }}
              >
                {isPrevew ? '查看' : '预览'}
              </Button>
            </div>
            {isPrevew ? (
              <div className="ai-panel-preview">
                {render(
                  JSON.parse(item.replace(/^```json/, '').replace(/```$/, '')),
                  {},
                  { theme: 'cxd' },
                )}
              </div>
            ) : (
              <ReactMarkdown
                components={{
                  code(props) {
                    const { children, className, node, ...rest } = props;
                    const match = /language-(\w+)/.exec(className || '');
                    return match ? (
                      <SyntaxHighlighter
                        {...rest}
                        PreTag="div"
                        children={String(children).replace(/\n$/, '')}
                        language={match[1]}
                        style={customStyle}
                      />
                    ) : (
                      <code {...rest} className={className}>
                        {children}
                      </code>
                    );
                  },
                }}
              >
                {item}
              </ReactMarkdown>
            )}
          </div>
        );
      }
      return (
        <div>
          <ReactMarkdown>{item}</ReactMarkdown>
        </div>
      );
    });
  };

  onWheel = (event: React.WheelEvent) => {
    if (event.deltaY < 0) {
      clearInterval(this.timer);
      this.timer = null;
    }
  };

  render() {
    const {
      conversations = [],
      leftPanelKey,
      changeLeftPanelKey,
    } = this.props.store;
    const { conversionState, value } = this.state;
    const showPause = conversionState !== ConversionState.idel;

    return (
      <Modal
        title="AI Assistant"
        footer={null}
        className={cls('ae-RendererPanel-modal', styles.modalClass)}
        open={leftPanelKey === 'ai' || this.props?.modalOpen}
        onCancel={() => {
          changeLeftPanelKey?.('outline');
          this.props?.setModalOpen(false);
        }}
        centered
        width={1000}
        height={720}
        zIndex={1401}
      >
        {/* 新版本的对话框架 */}
        <ProChatProvider>
        <div className="ae-RendererPanel">
        <Flex vertical className="ae-ai-panel">
          <AiPageGenerate insertString={(insertString) => this.insertSchemaNew(insertString)} />
        </Flex>
        </div>
        </ProChatProvider>
      </Modal>
    );
  }
}

export default AIPanel
