{"name": "amis-theme-editor-helper", "version": "2.0.26", "description": "amis主题编辑器通用方法", "main": "lib/index.js", "module": "esm/index.js", "types": "lib/index.d.ts", "scripts": {"test": "echo \"Warnings: no test specified\"", "build": "npm run clean-dist && cross-env NODE_ENV=production rollup -c ", "clean-dist": "rimraf lib/** esm/**", "i18n:update": "npx i18n update --config=./i18nConfig.js", "i18n:translate": "npx i18n translate --config=./i18nConfig.js --l=en-US", "i18n:merge": "npx i18n merge --config=./i18nConfig.js --l=en-US"}, "keywords": ["amis", "theme-editor-helper"], "author": "@fex", "license": "ISC", "files": ["lib", "esm"], "lint-staged": {"{src,scss,examples}/**/**/*.{js,jsx,ts,tsx,scss,json}": ["prettier --write"]}, "dependencies": {"amis": "*", "amis-core": "*", "amis-ui": "*", "style-inject": "^0.3.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.3.0", "@rollup/plugin-typescript": "^8.3.2", "@rollup/plugin-url": "^7.0.0", "@svgr/rollup": "^6.4.0", "@types/async": "^2.0.45", "@types/codemirror": "^5.60.5", "@types/deep-diff": "^1.0.0", "@types/history": "^4.6.0", "@types/hoist-non-react-statics": "^3.0.1", "@types/lodash": "^4.14.76", "@types/node": "^14.0.24", "@types/react": "^18.0.24", "@types/react-color": "^3.0.6", "@types/react-dom": "^18.0.8", "cross-env": "^7.0.3", "react": "^18.2.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "rollup": "^2.73.0", "rollup-plugin-auto-external": "^2.0.0", "rollup-plugin-license": "^2.7.0", "sass": "^1.49.7", "sass-loader": "^12.5.0", "style-loader": "^3.2.1", "tslib": "^2.3.1", "typescript": "^4.6.4"}, "peerDependencies": {"amis": "*", "amis-core": "*", "amis-ui": "*", "i18n-runtime": "1.0.10", "react": ">=16.8.6", "react-dom": ">=16.8.6"}}