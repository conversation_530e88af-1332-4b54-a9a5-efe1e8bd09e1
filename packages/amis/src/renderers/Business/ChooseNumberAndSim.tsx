import { FormItem } from 'amis';
import { ConfigProvider as ConfigProvider5 } from 'antd-v5';
import { ChooseNumberAndSim } from 'pos-components';
import React, { useEffect, useState } from 'react';

interface IQueryNumberListParamsProps {
  accNbrClassIds?: string;
  beginIndex: number;
  endIndex: number;
  accNbrLike: string;
  randomFlag: boolean;
  reserveInfo?: {
    staffId?: number;
    custId?: number;
  };
}

@FormItem({
  type: 'pad-pos-choose-number-and-sim',
  autoVar: true
})
export class IChooseNumberAndSim extends React.Component<any, any> {
  constructor(props: any) {
    super(props);
    this.state = {};
  }

  render() {
    return <ChooseNumberAndSimFunc {...this.props} />;
  }
}

const ChooseNumberAndSimFunc = (props: any) => {
  const {
    value,
    onChange,
    sourceGetNumberList,
    sourceLockNumber,
    sourceUnLockNumber,
    env: { fetcher },
    custInfo = {},
    loginInfo = {},
    dispatchEvent
  } = props;

  const [numberList, setNumberList] = useState<any>([]);

  const ReservedTypeList = [
    {
      label: 'All Type',
      value: 'all'
    },
    {
      label: 'Staff Reserved',
      value: 'S'
    },
    {
      label: 'Customer Reserved',
      value: 'C'
    }
  ];

  const SimType = [
    {
      label: 'Physical SIM',
      value: 'P'
    },
    {
      label: 'ESIM',
      value: 'E'
    }
  ];

  // query number list
  const queryNumberList = async (data?: any) => {
    if (!sourceGetNumberList) {
      return;
    }

    const res = await fetcher({
      url: sourceGetNumberList.url,
      method: sourceGetNumberList.method,
      data: data || {}
    });

    setNumberList(
      (res?.data?.accNbrList || []).map((item: any) => ({
        label: item.accNbr,
        value: item.accNbr,
        ...item
      }))
    );
  };

  // search query number list
  const onSearchListFn = (iData: any) => {
    const { selectedValue = {}, inputValue = '' } = iData || {};
    const reservedType = selectedValue.value;

    let reserveInfo;

    if (reservedType === 'S') {
      reserveInfo = {
        staffId: (loginInfo?.jobData as Array<any>)?.[0]?.staffId
      };
    } else if (reservedType === 'C') {
      reserveInfo = {
        custId: custInfo?.custId
      };
    }

    const dataParams: IQueryNumberListParamsProps = {
      randomFlag: true,
      accNbrLike: inputValue,
      beginIndex: 0,
      endIndex: 14,
      reserveInfo
    };
    queryNumberList(dataParams);
  };

  // on sim card info change
  const getData = (value: any) => {
    onChange?.({
      chooseNumberAndSimData: value
    });
    dispatchEvent('onSimCardInfoChange', {
      chooseNumberAndSimData: value
    }); // 号码信息改变
  };

  // lock number
  const lockNumber = ({ prefix, accNbr }: any) => {
    if (!sourceLockNumber) {
      return;
    }

    fetcher({
      url: sourceLockNumber.url,
      method: sourceLockNumber.method,
      data: {
        resourceList: [
          {
            contactChannelId: 84,
            prefix,
            operationType: 'LK',
            resSpecCode: 'ACC_NBR',
            accNbr,
            operationInfo: {
              partyCode: (
                loginInfo?.jobData as Array<any>
              )?.[0]?.staffId?.toString(),
              partyType: 'F'
            }
          }
        ]
      }
    });
  };

  // unlock number
  const unLockNumber = ({ prefix, accNbr }: any) => {
    if (!sourceUnLockNumber) {
      return;
    }

    fetcher({
      url: sourceUnLockNumber.url,
      method: sourceUnLockNumber.method,
      data: {
        resourceList: [
          {
            contactChannelId: 84,
            prefix,
            operationType: 'UL',
            resSpecCode: 'ACC_NBR',
            accNbr,
            operationInfo: {
              partyCode: (
                loginInfo?.jobData as Array<any>
              )?.[0]?.staffId?.toString(),
              partyType: 'F'
            }
          }
        ]
      }
    });
  };

  useEffect(() => {
    const params: IQueryNumberListParamsProps = {
      randomFlag: true,
      accNbrLike: '',
      beginIndex: 0,
      endIndex: 14
    };
    queryNumberList(params);
  }, []);

  return (
    <ConfigProvider5 prefixCls="ant5">
      <ChooseNumberAndSim
        reservedTypeList={ReservedTypeList}
        simTypeList={SimType}
        numberList={numberList}
        onSearchListFn={onSearchListFn}
        getAllValuesFn={getData}
        unLockNumber={unLockNumber}
        lockNumber={lockNumber}
      />
    </ConfigProvider5>
  );
};
