import { Ren<PERSON><PERSON>, RendererProps, uuidv4 } from 'amis-core';
import { Typography } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import classNames from 'classnames';
import React from 'react';
import customClassNameFishInPageDesigner from '../../constant/customClassNameFishInPageDesigner';

const { Title, Paragraph } = Typography;

interface AgreementProps extends RendererProps {
  agreementTitle?: string;
  agreementContent?: string;
  required?: boolean;
  showCheckbox?: boolean;
}

@Renderer({
  type: 'crm-agreement',
  autoVar: true,
})
export class Agreement extends React.Component<AgreementProps, any> {
  state = {};

  constructor(props: any) {
    super(props);
  }

  id = uuidv4();

  componentDidMount() {
    const id = this.id;
    const that: any = this;
    const { mockData } = this.props;

    window?.require?.(
      ['crm/standardizedcomponents/pos/components/AgreementView.js'],
      function (View: any) {
        that.view = new View({
          el: $(`#${id}`),
          ...mockData,
        });

        that.view.render();
      },
    );
  }

  handleChange = (e: CheckboxChangeEvent) => {};

  render() {
    const { className, style } = this.props;

    return (
      <div
        className={classNames(className, customClassNameFishInPageDesigner)}
        style={{
          overflow: 'auto',
        }}
        id={this.id}
      ></div>
    );
  }
}
