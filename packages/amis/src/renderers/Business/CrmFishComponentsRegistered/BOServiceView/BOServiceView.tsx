import { ScopedContext } from 'amis';
import { IScopedContext, Renderer, RendererProps, uuidv4 } from 'amis-core';
import { Button } from 'antd';
import React from 'react';
import { ContainerSchema } from '../../../Container';

export interface BOServiceProps
  extends RendererProps,
    Omit<ContainerSchema, 'type' | 'className' | 'style'> {
  children?: (props: any) => React.ReactNode;
}

@Renderer({
  type: 'crm-bo-service',
  autoVar: true,
})
export default class BOService extends React.Component<BOServiceProps, any> {
  state = {};

  view: any;

  Controller: any;

  DataInput: any;

  bfmKey: string;

  static contextType = ScopedContext;

  instance: any;

  constructor(props: any, context: IScopedContext) {
    super(props);

    const scoped = context;
    scoped.registerComponent(this);
  }

  id = uuidv4();

  componentDidMount() {
    const id = this.id;
    const that: any = this;
    const { opt } = this.props;
    console.log(opt);

    const options = {
      ...this.props,
      // flowInitData: {
      //   OFFER_ID: '100380',
      //   SUBS_EVENT_ID: 32,
      //   SUBS_ID: '301957099',
      //   AREA_ID: 1,
      //   ORG_ID: 1,
      //   STAFF_JOB_ID: 1,
      //   STAFF_ID: 1,
      //   SUBS_PLAN_ID: '100522',
      //   SERV_TYPE: '15',
      //   CUST_ID: '30962236',
      //   ACCT_ID: '30926672',
      //   INDEP_PROD_SPEC_ID: '100380',
      //   CONTACT_CHANNEL_ID: 1,
      //   CHANNEL_TYPE: '1',
      //   PROD_STATE: 'A',
      //   A_PARTY_TYPE: null,
      //   A_PARTY_CODE: null,
      //   SP_ID: 0,
      //   READ_CARD_FLAG: 'N',
      // },
    };

    window?.require?.(
      [
        'crm/standardizedcomponents/views/BOServiceView.js',
        // 'crm/modules/fbf/bfm/models/DataInput',
      ],
      function (View: any, DataInput: any) {
        that.view = View;
        that.instance = new View({
          ...options,
          el: $(`#${id}`),
          callBackFn: that.callBackFn,
          dispatchController: that.saveController,
          dispatchBFMKey: that.dispatchBFMKey,
        });

        that.instance.render();

        // 启动一个流程
        if (opt) {
          that.startFlow();
        }
      },
    );
  }

  // Fish中的值抛出来，传入给B组件
  callBackFn = (value: any) => {
    console.log('value', value);
  };

  saveController = (Controller: any) => {
    this.Controller = Controller;
    console.log('Controller', Controller);
  };

  registerOrderItem = (orderItemId: string) => {
    console.log('registerOrderItem');
    // const commonDataId = this.Controller.getInstance().getCommonDataId(
    //   this.instance
    // );
    // console.log(commonDataId);
    // this.Controller.getInstance().registerOrderItem(orderItemId, this.view);
    // this.Controller.getInstance().registerCommonDataId(options.flowData.commonDataId, this);
  };

  dispatchBFMKey = (flowData: string) => {
    // this.bfmKey = bfmKey;
    // this.props.dispatchEvent('startFlowCallBack', bfmKey);
    const newFlowData = this.handleFlowData(flowData);
    console.log('2 处理后的flowData');
    console.log(newFlowData);
    this.props.dispatchEvent('startFlowCallBack', newFlowData);
  };

  // 处理从Fish中获取到的flowData
  handleFlowData = (flowData: {
    wizardPage: string;
    dirtyList: Array<any>;
  }) => {
    const wizardPage = flowData.wizardPage;
    let orderItem = null;
    let custOrder = null;
    if (flowData.dirtyList && flowData.dirtyList[0]) {
      if (flowData.dirtyList[0].value) {
        custOrder = flowData.dirtyList[0].value;
        if (custOrder.ORDER_ITEM || !fish.isEmpty(custOrder.ORDER_ITEM)) {
          orderItem = custOrder.ORDER_ITEM[0];
          const bundle = fish.find(
            custOrder.ORDER_ITEM,
            function (item) {
              return item.SERV_TYPE == '51';
            }.bind(this),
          );
          if (bundle) {
            orderItem = bundle;
          }
        }
        // this.custOrderId = custOrder.CUST_ORDER_ID;
        // this.cust = custOrder.CUST;
        // this.custOrder = custOrder;
      }
    }
    const initData = {
      wizardPage,
      orderItem: orderItem,
      custOrder: custOrder,
      flowData: flowData,
      printType: 'INVOICE',
      // showPreAccNbr: this.showPreAccNbr,
      // pageUuid: this.pageUuid,
    };

    return initData;
  };

  startFlow = async () => {
    try {
      const { opt, mockData } = this.props;
      console.log(mockData);

      // if (!opt) {
      //   console.warn('启动流程所需的 opt 参数不存在');
      //   return;
      // }

      if (this.instance) {
        console.log('1 套餐信息');
        console.log(opt);
        if (typeof this.instance.initializeData === 'function') {
          this.instance.initializeData(opt || mockData);
        }
        if (typeof this.instance.startOrderFlowWithSubsplan === 'function') {
          await this.instance.startOrderFlowWithSubsplan();
        }
      } else {
        console.error('this.instance 未正确初始化，无法启动流程');
      }
    } catch (error) {
      console.error('启动流程时发生错误:', error);
    }
  };

  render() {
    const {
      className = '',
      style,
      wrapperComponent = 'div',
      body,
      render,
      children,
      classnames: cx,
    } = this.props;

    const Component = wrapperComponent as keyof JSX.IntrinsicElements;
    const content = children || (body ? render('body', body) : null);

    return (
      <Component className={cx('Container', className)} style={style}>
        <div className={cx('Container-body')}>{content}</div>
        <div>
          <Button onClick={this.startFlow}>启动流程</Button>
        </div>
      </Component>
    );
  }
}
