import { Renderer, RendererProps, uuidv4 } from 'amis-core';
import classNames from 'classnames';
import React from 'react';
import customClassNameFishInPageDesigner from '../../constant/customClassNameFishInPageDesigner';

interface CouponProps extends RendererProps {}

@Renderer({
  type: 'crm-coupon',
  autoVar: true,
})
export class Coupon extends React.Component<CouponProps, any> {
  state = {};

  constructor(props: any) {
    super(props);
  }

  id = uuidv4();

  // 渲染 fish 组件
  renderFishComponent = (options: { [key: string]: any } = {}) => {
    this.removeFishComponent();
    const curId = this.id;
    const that: any = this;
    window.require(
      // ['crm/modules/pos/flowpage/common/views/GoodsCouponView.js'],
      ['crm/standardizedcomponents/pos/flowpage/common/views/GoodsCouponView.js'],
      function (View: any) {
        that.myView = new View({
          ...options,
          el: $(`#${curId}`),
        });
        that.myView.render();
      },
    );
  };

  // 卸载 fish 组件
  removeFishComponent = () => {
    const escapedId = CSS.escape(this.id);
    const myDiv: any = document.querySelector?.(`#${escapedId}`);

    if (myDiv) {
      while (myDiv.firstChild) {
        myDiv.removeChild(myDiv.firstChild);
      }
    }
  };

  componentDidMount() {
    const opt = {
      custOrder: {
        SERV_TYPE: '15',
        ACCEPT_DATE: '2025-02-28 13:47:52',
        CUST_ORDER_ID: ****************,
        OFFER_ID: '207798',
        CONTACT_CHANNEL_ID: 1,
        ORDER_ITEM: [
          {
            SERV_TYPE: 15,
            DP_OFFER_ORDER: [
              {
                SP_ID: 0,
                SUBSCRIBE_METHOD: 'OFFER_GROUP_DEFAULT',
                OFFER_TYPE: '4',
                EFF_TYPE: 'B',
                OFFER_GROUP_ID: 101528,
                OFFER_ID: 207803,
                OFFER_SEQ: '54ede246-2240-474c-9202-579c0da2e0e8',
                TRIGGER_OBJ_ID: 101528,
                DEFAULT_FLAG: 'Y',
                OFFER_NAME: 'GSM_postpaid_ucc',
                OPERATION_TYPE: 'A',
                EXITS_AGREEMENT_FLAG: 'N',
                ORDER_ITEM_ID: ****************,
                HIDE_FLAG: 'Y',
              },
            ],
            ACCT: {
              PAYMENT_METHOD_ID: 1,
              CUST_ID: ********,
              ACCT_ID: ********,
              STATE: 'A',
              BILLING_CYCLE_TYPE_ID: 2,
              ACCT_NAME: 'Lucci 22801',
              ACCT_NBR: '***********',
              POSTPAID: 'Y',
            },
            SUBS_PLAN_NAME: 'GSM_postpaid_ucc',
            CUST_ORDER_ID: ****************,
            CUST_INFO: {
              CUST_ID: ********,
              CUST_TYPE: 'A',
            },
            OFFER_ID: 207798,
            SUBS_EVENT_ID: 1,
            PARTY_TYPE: 'A',
            ORDER_NBR: '****************',
            ACCT_ID: ********,
            IS_RESERVE: false,
            ORDER_TYPE: 'B',
            OPERATION_TYPE: 'A',
            PART_ID: 302,
            IS_BOOKING: true,
            SUBS_PLAN_ID: 207802,
            CUST_ID: ********,
            STAFF_ID: 1,
            SP_ID: 0,
            SIM_TYPE_ID: 1,
            ROUTING_ID: 1,
            STATE_DATE: '2025-02-28 13:47:52',
            ORDER_STATE: 'I',
            RES_RESERVE_INST_ID: '****************',
            POSTPAID: 'Y',
            SUBS_BASE_ORDER: {
              SERV_TYPE: 15,
              CUST_ID: ********,
              ORG_ID: 1,
              USER_ID: ********,
              SUBS_PLAN_NAME: 'GSM_postpaid_ucc',
              AREA_NAME: 'Default Area(Please modify)',
              ROUTING_ID: 1,
              DEF_LANG_ID: 30,
              POSTPAID: 'Y',
              AREA_ID: 1,
              DEF_LANG_NAME: 'Bahasa',
              INDEP_PROD_SPEC_ID: 207798,
              ACCT_ID: ********,
              USER_NAME: 'Lucci 22801',
              OPERATION_TYPE: 'A',
              PART_ID: 302,
              SUBS_PLAN_ID: 207802,
              ORDER_ITEM_ID: ****************,
              ACCT_NBR: '***********',
              CUST_NAME: 'Lucci 22801',
            },
            BUNDLE_MEMBER_FLAG: 'N',
            SUBS_ID: -********,
            OFFER_NAME: 'GSM_postpaid',
            IS_JOB_OPERATION: false,
            ORDER_ITEM_ID: ****************,
            CUST_NAME: 'Lucci 22801',
            PARTY_CODE: '1',
            childOrderList: [],
            url: 'crm/modules/pos/flowpage/business/newconnection/views/GSMNewSubsPlanView',
          },
        ],
        CUST_ORDER_VER: {
          UPDATE_DATE: '2025-02-28 13:47:52',
          CREATED_DATE: '2025-02-28 13:47:52',
          STATE: 'A',
          CUST_ORDER_ID: ****************,
          CUST_ORDER_VER_ID: 2502010114990636,
        },
        CUST: {
          CUST_ID: ********,
          ZIPCODE: '52100',
          CUST_TYPE: 'A',
          STD_ADDR_ID: 521000,
          ADDRESS: '11111',
          CUST_CODE: '1********',
          CERT_ID: ********,
          CUST_NAME: 'Lucci 22801',
        },
        SUBS_EVENT_ID: 1,
        CUST_ORDER_STATE: 'I',
        PARTY_TYPE: 'A',
        PART_ID: 302,
        CUST_ID: ********,
        CREATED_DATE: '2025-02-28 13:47:52',
        SP_ID: 0,
        HANDLED_BY_ACCT_MANAGER: 'N',
        CUST_ORDER_ATTR: [
          {
            ROUTING_ID: 1,
            SP_ID: 0,
            ATTR_ID: 100687,
            CUST_ORDER_ID: ****************,
            VALUE: '***********',
            PART_ID: 302,
          },
          {
            ROUTING_ID: 1,
            SP_ID: 0,
            ATTR_ID: 100688,
            CUST_ORDER_ID: ****************,
            VALUE: '<EMAIL>',
            PART_ID: 302,
          },
        ],
        ROUTING_ID: 1,
        A_PARTY_CODE: '********',
        RES_RESERVE_INST_ID: '****************',
        IS_V9_SERVICE_MODE: 'Y',
        SUBS_EVENT_NAME: 'New Connection',
        CUST_ORDER_NBR: '****************',
        CREDIT_LIMIT_MODE: 'O',
        A_PARTY_TYPE: 'D',
        STAFF_INFO: {
          AREA_ID: 1,
          ORG_ID: 1,
          ORG_TYPE: 'A',
          STAFF_ID: 1,
          STAFF_JOB_ID: 1,
        },
        PARTY_CODE: '1',
      },
    };

    this.renderFishComponent(opt);
  }

  componentDidUpdate(prevProps: Readonly<CouponProps>): void {}

  render() {
    const { className, style } = this.props;
    return (
      <div
        className={classNames(className, customClassNameFishInPageDesigner)}
        style={style}
        id={this.id}
      ></div>
    );
  }
}
