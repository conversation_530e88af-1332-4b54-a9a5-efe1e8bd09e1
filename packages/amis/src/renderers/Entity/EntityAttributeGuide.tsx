import {Ren<PERSON><PERSON>} from 'amis-core';
import React, {
  useEffect,
  useState,
  useMemo,
} from 'react';
import { Input, Table, Select } from 'antd';
import type { SortableContainerProps, SortEnd } from 'react-sortable-hoc';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { MenuOutlined } from '@ant-design/icons';

const toTitleCase = (str) => {
  return str
    .split('-')
    .map(s => s.charAt(0).toUpperCase() + s.slice(1))
    .join(' ');
}
const eachNameEnum:any = {
  'sub-cfs': 'fnCfsList',
  'rfs': 'assignedRes',
  'res': 'members',
};

const specIdKeyEnum:any = {
  'sub-cfs': 'cfsSpecId',
  'rfs': 'rfsSpecId',
  'res': 'RES_SPEC_ID',
};

const specialContainer:any = {
  'anchor-nav': 'links',
  tabs: 'tabs',
  'input-table': 'columns',
  table: 'columns',
  crud: 'columns',
  'button-group': 'buttons',
  flex: 'items',
  combo: 'items',
  each: 'items',
  property: 'items',
  operation: 'buttons',
};

const formItemOptions = [
  {
    label: 'text',
    value: 'input-text',
  },
  {
    label: 'number',
    value: 'input-number',
  },
  {
    label: 'textarea',
    value: 'textarea',
  },
  {
    label: 'select',
    value: 'select',
  },
  {
    label: 'date',
    value: 'input-date',
  },
  {
    label: 'dateRange',
    value: 'input-date-range',
  },
  {
    label: 'month',
    value: 'input-month',
  },
  {
    label: 'year',
    value: 'input-year',
  },
  {
    label: 'datetime',
    value: 'input-datetime',
  },
  {
    label: 'radio',
    value: 'radios',
  },
  {
    label: 'checkbox',
    value: 'checkboxes',
  },
  {
    label: 'treeSelect',
    value: 'tree-select',
  },
  {
    label: 'switch',
    value: 'switch',
  },
  {
    label: 'static',
    value: 'static',
  },
];

// 获取当前代码内的所有带_attrId的属性
const getCodeAttrIdArr = (code: any[]) => {
  const attrArr: any[] = [];

  function getCodeAttr(arr: any[]) {
    (arr || []).forEach((item: any) => {
      if (item._attrId) {
        attrArr.push({ ...item });
      }
      if (item.body && Array.isArray(item.body)) {
        getCodeAttr(item.body);
      }
      if (item.items && Array.isArray(item.items)) {
        getCodeAttr(item.items);
      }
      if (item.tabs && Array.isArray(item.tabs)) {
        getCodeAttr(item.tabs);
      }
    });
  }

  getCodeAttr(code);
  return attrArr;
}

const modifyDeepestBody = (rootNode, modifier) => {
  let maxDepth = -1;
  let targetNode = rootNode;

  // 递归遍历函数
  const traverse = (node, currentDepth) => {
    const boxName = specialContainer[node.type] || 'body';
    if (node[boxName]?.constructor === Array) {
      if (currentDepth > maxDepth) {
        maxDepth = currentDepth;
        targetNode = node;
      }
      if (node?.type !== 'each') {
        node[boxName].forEach((child) => traverse(child, currentDepth + 1));
      }
    }
  };

  traverse(rootNode, 0); // 从根节点开始遍历

  // 执行修改操作
  if (targetNode) {
    modifier(targetNode);
  }
}

const DragHandle = SortableHandle(() => <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />);

const SortableItem = SortableElement((props: React.HTMLAttributes<HTMLTableRowElement>) => <tr {...props} />);
const SortableBody = SortableContainer((props: React.HTMLAttributes<HTMLTableSectionElement>) => <tbody {...props} />);

const AttrGuide = (props) => {
  const { env, data, entityType } = props;
  const [specList, setSpecList] = useState([]);
  const [specValue, setSpecValue] = useState();
  const [loading, setLoading] = useState(false);
  const [attrList, setAttrList] = useState([]);
  const [filterValue, setFilterValue] = useState('');
  const [selectedAttrKey, setSelectedAttrKey] = useState([]);

  const isEdit = data?._guideData && data._guideData?.specId;
  const idKey = entityType === 'so' ? 'attrCode' : 'attrId';
  const isMultiple = ['sub-cfs', 'rfs', 'res'].includes(entityType);

  const tableTitle = toTitleCase(data?.type);

  const baseColumns = [
    {
      title: 'Attribute Name',
      dataIndex: 'attrName',
      key: 'attrName',
    },
    {
      title: 'Attribute Code',
      dataIndex: 'attrCode',
      key: 'attrCode',
    },
    {
      title: 'Is Mandatory',
      dataIndex: 'isMandatory',
      key: 'isMandatory',
    },
    {
      title: 'Data Type',
      dataIndex: 'dataType',
      key: 'dataType',
    },
    {
      title: 'Component Type',
      dataIndex: 'componentType',
      key: 'componentType',
      render: (text: any, row: any, index: any) => {
        return (
          <Select
            value={text}
            options={formItemOptions}
            style={{ width: '100%' }}
            onClick={(e) => {
              e.stopPropagation();
            }}
            dropdownStyle={{zIndex: 1600}}
            onChange={(val) => {
              row.componentType = val;
              setAttrList(attrList.map((item) => (item[idKey] === row[idKey] ? row : item)));
            }}
          />
        );
      },
    },
  ];

  const sortColumn = {
    title: 'Sort',
    dataIndex: 'sort',
    width: 65,
    className: 'drag-visible',
    render: () => <DragHandle />,
  };

  const attrColumns = isMultiple
    ? [
        sortColumn,
        {
          title: 'Specification Name',
          dataIndex: 'specName',
          key: 'specName',
        },
        ...baseColumns,
      ]
    : [sortColumn, ...baseColumns];

  const onSortEnd = ({ oldIndex, newIndex }: SortEnd) => {
    // if (oldIndex !== newIndex) {
    //   const newData = arrayMoveImmutable(attrList.slice(), oldIndex, newIndex).filter(
    //     (el: DataType) => !!el,
    //   );
    //   setAttrList(newData);
    // }
    // 获取过滤后数据的实际元素
    const movedItem = filteredAttr[oldIndex];

    // 在原始数据中定位该元素
    const originalOldIndex = attrList.findIndex((item) => item[idKey] === movedItem[idKey]);

    // 计算在原始数据中的新位置
    const originalNewIndex = attrList.findIndex((item) => item[idKey] === (filteredAttr[newIndex][idKey] || attrList[attrList.length - 1][idKey]));

    if (originalOldIndex !== originalNewIndex) {
      const newData = [...attrList];
      const [removed] = newData.splice(originalOldIndex, 1);
      newData.splice(originalNewIndex, 0, removed);
      setAttrList(newData);
    }
  };

  const DraggableContainer = (props: SortableContainerProps) => (
    <SortableBody useDragHandle disableAutoscroll helperClass="row-dragging" onSortEnd={onSortEnd} {...props} />
  );

  const DraggableBodyRow: React.FC<any> = ({ className, style, ...restProps }) => {
    // function findIndex base on Table rowKey props and should always be a right array index
    const index = filteredAttr.findIndex((x) => x[idKey] === restProps['data-row-key']);
    return <SortableItem className={className} index={index} {...restProps} />;
  };
  
  const components = {
    body: {
      wrapper: DraggableContainer,
      row: DraggableBodyRow,
    },
  };

  const existAttr = useMemo(() => {
    if (isEdit) {
      return getCodeAttrIdArr(data?.body || []);
    }
    return [];
  }, [data]);

  const handleAttrList = (arr: any, specs?: any) => {
    if (isEdit) {
      setSelectedAttrKey(existAttr.map((item) => item._attrId));
    }
    const filterRes = (arr || []).map((item) => {
      const existItem = existAttr.find((attr) => attr._attrId === item[idKey]);

      let componentType = 'input-text';
      // 如果修改了类型，那么优先采用修改的类型
      if (isEdit && existItem) {
        componentType = existItem.type;
      } else if (item.sourceType && ['Enum', 'Dict'].includes(item.sourceType)) {
        componentType = 'select';
      } else if (item.dataType === 'N' || item.dataType === 'I') {
        componentType = 'input-number';
      } else if (item.dataType === 'D') {
        componentType = 'input-date';
      }
      // eslint-disable-next-line no-nested-ternary
      const specIdKey = specIdKeyEnum[entityType] || 'rfsSpecId';
      const specId = item[specIdKey];
      return {
        ...item,
        componentType,
        specName: isMultiple ? (specs || specList).find((spec) => spec.ID === specId)?.NAME : undefined,
      };
    });
    // const treeData = createFilteredTree(res.resultData || [], (node) => (node.IS_FC === 'N' && node.NODE_TYPE === 'spec') || (node.NODE_TYPE === 'catalog' && node.children.length > 0));
    setAttrList(filterRes);
  };

  const getAttrList = async (value) => {
    try {
      setLoading(true);

      if (entityType === 'so') {
        const soRes = await env.fetcher(`/design/cfsFixAttr/v1?cfsSpecId=${value}`);
        // const cfsAttr = (res.cfsSpecAttrList || []).filter((item) => item.isOrdAttr === 'Y');
        const allAttr = soRes.data.fixAttrList;
        handleAttrList(allAttr);
      } else {
        const res = await env.fetcher(`/design/cfsSpec/attributes/v1/${value}`);
        const cfsAttr = (res.data.cfsSpecAttrList || []).filter((item) => item.isOrdAttr === 'N');
        handleAttrList(cfsAttr);
      }
    } catch (error) {
      console.error('getAttrList', error);
    } finally {
      setLoading(false);
    }
  };

  const getAllAttrList = (specIds: any[], specs?: any) => {
    const coverFunction = [];
    specIds.forEach((spec: any) => {
      const getAttr = new Promise(async (resolve, reject) => {
        if (entityType === 'res') {
          const res = await env.fetcher({
            // url: '/im/meta/getObjAttrAndValues/v1',
            url: 'https://mock.iwhalecloud.com/mock/4115/res/attrlist',
            method: 'POST',
            headers: {
              'X-Csrf-Token': '8b3c5b22-bc52-4603-955f-f0af7c977bc5',
            }
          }, {
            OBJ_TYPE: spec,
            IS_INHERITED: 'Y',
            SORT_NAME: '',
            SORT_ORDER: 'asc',
            SERVICE_NAME: 'MIM_GET_OBJ_ATTRIBUTE',
          });
          const attrs = (res.data.resultData || []).map((item) => {
            return { 
              ...item,
              attrId: `${spec}-${item.ATTR_ID}`,
              attrName: item.ATTR_NAME,
              attrCode: item.ATTR_CODE,
              isMandatory: item.IS_MANDATORY,
              dataType: item.DATA_TYPE,
              sourceType: item.SOURCE_TYPE,
              RES_SPEC_ID: spec,
              values: (item.VALUES || []).map(
                item => {
                  return {
                    valueDesc: item.DESC_VALUE,
                    value: item.ID_VALUE,
                  };
                }
              ) 
            };
          });
          resolve(attrs);
        } else {
          const url = entityType === 'rfs' ? `/design/rfsSpec/attributes/v1?rfsSpecId=${spec}` : `/design/cfsSpec/attributes/v1/${spec}`;
          const res = await env.fetcher(url);
          const attrArr = entityType === 'rfs' ? res.data.rfsSpecAttrList : res.data.cfsSpecAttrList;
          const cfsAttr = (attrArr || []).filter((item) => item.isOrdAttr === 'N');
          resolve(cfsAttr);
        }
      });
      coverFunction.push(getAttr);
    });

    if (coverFunction && coverFunction.length) {
      setLoading(true);
      Promise.allSettled(coverFunction)
        .then((res) => {
          let result = [];
          res.forEach((item: any) => {
            result = [...result, ...item.value];
          });
          handleAttrList(result, specs);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  const getSpecList = async () => {
    try {
      setLoading(true);
      const res = await env.fetcher('/design/catalogs/v1');
      const isFcValue = entityType === 'Sub CFS Attribute' ? 'Y' : 'N';
      const filterRes = (res.data.resultData || []).filter((item) => item.IS_FC === isFcValue && item.NODE_TYPE === 'spec');
      setSpecList(filterRes);
      // 如果是编辑，那么初始化导航弹窗
      if (isEdit) {
        const { specId } = data._guideData;
        if (isMultiple) {
          getAllAttrList(specId, filterRes);
        } else {
          getAttrList(specId);
        }
        setSpecValue(specId);
      }
    } catch (error) {
      console.error('getSpecList', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAttrSearch = (e) => {
    setFilterValue(e.target.value);
  };

  const specChange = (value) => {
    setSpecValue(value);
    if (isMultiple) {
      getAllAttrList(value);
    } else {
      getAttrList(value);
    }
  };

  const getFormItemName = (item) => {
    if (entityType === 'cfs') {
      return `mainCfs.cfsAttr.${item.attrCode}`;
    }
    if (entityType === 'subCfs') {
      return `fnCfsList[\${index}].cfsAttr.${item.attrCode}`;
    }
    if (entityType === 'rfs') {
      return `assignedRes[\${index}].rfsAttr.${item.attrCode}`;
    }
    if (entityType === 'res') {
      return `members[\${index}].${item.attrCode}`;
    }
    if (item.isOrdAttr === 'Y') {
      return `omSoAttrs.${item.attrCode}`;
    }
    return item.attrCode;
  };

  const getSchemaByAttr = (attrList) => {
    return attrList.map((item) => {
      const existItem = existAttr.find((attr) => attr._attrId === item[idKey]);
      if (isEdit && existItem) {
        return {
          ...existItem,
          type: item.componentType,
        };
      }
      const specIdKey = specIdKeyEnum[entityType] || 'rfsSpecId';
      const visibleOn = `\${${specIdKey} === '${item[specIdKey]}'}`;

      const schema: any = {
        type: item.componentType,
        name: getFormItemName(item),
        label: item.attrName,
        required: item.isMandatory === 'Y',
        disabled: item.disabled,
        value: item.defaultVal,
        _attrId: item[idKey],
        visibleOn: isMultiple ? visibleOn : undefined,
      };
      if (item.componentType === 'input-text') {
        schema.maxLength = item.len;
      } else if (item.componentType === 'select') {
        schema.options = item.values || [];
        schema.labelField = 'valueDesc';
        schema.valueField = 'value';
      } else if (item.componentType === 'input-number') {
        schema.min = item.minVal;
        schema.max = item.maxVal;
      } else if (item.componentType === 'input-date' || item.componentType === 'input-date-range') {
        schema.format = 'YYYY-MM-DD';
      } else if (item.componentType === 'input-datetime') {
        schema.format = 'YYYY-MM-DD HH:mm:ss';
      } else if (item.componentType === 'input-month') {
        schema.format = 'YYYY-MM';
      } else if (item.componentType === 'input-year') {
        schema.format = 'YYYY';
      }
      return schema;
    });
  };

  const getFormData = () => {
    const selectRow = attrList.filter((item) => selectedAttrKey.includes(item[idKey]));
    const resBodySchema = getSchemaByAttr(selectRow);
    // const resBodySchema = isMultiple && !isRes ? [...(isSub ? subFixAttr : rfsFixAttr), ...bodySchema] : bodySchema;
    const rootObj = {
      ...data,
      _guideData: {
        entityType,
        specId: specValue,
      },
    };
    modifyDeepestBody(rootObj, (targetNode: any) => {
      const isEach = targetNode?.type === 'each';
      const boxName = specialContainer[targetNode.type] || 'body';
      const noAttrNode = (targetNode[boxName] || []).filter(
          (item) => !item._attrId
        );
      if (isMultiple && !isEach) {
        targetNode.body = [
          {
            type: 'each',
            name: eachNameEnum[entityType],
            items: [
              ...targetNode.body,
              ...resBodySchema,
            ],
          },
        ];
      } else {
        // targetNode[boxName] = resBodySchema;
        targetNode[boxName] = [...noAttrNode, ...resBodySchema];
      }
    });
    return rootObj;
  }

  const filteredAttr = useMemo(() => {
    return attrList.filter((item) => item.attrName.toLowerCase().includes(filterValue.toLowerCase()));
  }, [attrList, filterValue]);

  const attrRowSelection = {
    selectedRowKeys: selectedAttrKey,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedAttrKey(selectedRowKeys);
    },
    type: 'checkbox',
  };

  useEffect(() => {
    const res = getFormData()
    props.formStore.setValues(res);
  }, [selectedAttrKey])

  useEffect(() => {
    getSpecList();
  }, []);
  
  return (
    <div>
      <div className="flex justify-between mb-4">
          <div>{tableTitle} </div>
          <div>
            <Select
              value={specValue}
              placeholder="Please Select"
              options={specList}
              mode={isMultiple ? 'multiple' : undefined}
              maxTagCount="responsive"
              showSearch
              filterOption={(input, option) => (option?.NAME ?? '').toLowerCase().includes(input.toLowerCase())}
              fieldNames={{ label: 'NAME', value: 'ID' }}
              onChange={specChange}
              // zIndexPopup={1610}
              dropdownStyle={{zIndex: 1600}}
              style={{ width: isMultiple ? 600 : 300, marginRight: 12 }}
            />

            <Input.Search placeholder="Please Enter" onChange={handleAttrSearch} style={{ width: 300 }} />
          </div>
        </div>
        <Table
            columns={attrColumns}
            dataSource={filteredAttr}
            rowKey={idKey}
            loading={loading}
            rowSelection={attrRowSelection}
            components={components}
            onRow={(record, index) => {
              return {
                onClick: () => {
                  const existId = selectedAttrKey.includes(record[idKey]);
                  if (existId) {
                    setSelectedAttrKey(selectedAttrKey.filter((item) => item !== record[idKey]));
                  } else {
                    setSelectedAttrKey(selectedAttrKey.concat(record[idKey]));
                  }
                },
                index,
                // 'data-row-key': record[idKey],
              };
            }}
            pagination={false}
            scroll={{ y: 400 }}
          />
    </div>
  )

}

@Renderer({
  type: 'entity-attribute-guide',
  autoVar: true
})

export default class EntityAttributeGuide extends React.Component<any, any> {
  render() {
    const {items = [], render, initMenu} = this.props;
    console.log('this.props', this.props);
    return (
      <AttrGuide {...this.props}></AttrGuide>
    );
  }
}
