{"name": "aisuda", "workspaces": ["packages/amis-formula", "packages/amis-core", "packages/amis-ui", "packages/amis", "packages/amis-editor-core", "packages/amis-editor", "packages/amis-theme-editor-helper"], "scripts": {"build": "npm run build --workspaces", "build-editor": "vite build --base=./", "build-esm": "npm run build-esm --workspaces", "coverage": "jest --coverage --collectCoverage=v8", "deploy-gh-page": "sh ./deploy-gh-pages.sh", "fis3": "concurrently --restart-tries -1 npm:fis3-serve npm:fis3-dev", "fis3-dev": "fis3 release -cwd ./public", "fis3-serve": "fis3 server start --www ./public --port 8888 --no-daemon --no-browse", "fis3-stop": "fis3 server stop", "gen": "node registeredComponentsUtils/generateTp.js && node registeredComponentsUtils/modifyMinimals.js", "getChangeLog": "ts-node ./scripts/getChangeLog", "prepare": "husky install", "publish": "npx publish", "release": "npm run build --workspaces && lerna publish from-package --registry=https://registry.npmjs.org --ignore-scripts", "revision": "ts-node ./scripts/generate-revision.ts", "start": "vite", "stylelint": "npx stylelint 'packages/**/*.scss'", "sync-crm-and-cvbs-modules": "node registeredComponentsUtils/syncRemoteCrmAndCvbs.js", "remove-crm-and-cvbs-modules": "node registeredComponentsUtils/removeCrmAndCvbs.js", "sync-portal-source": "node fishUtils/downloadPortalSrc.js", "test": "npm test --workspaces", "typecheck": "tsc --noEmit", "update-snapshot": "npm run update-snapshot --workspaces", "version": "lerna version", "compare-git-time": "node fishUtils/script.compare-git-time.js", "find-files-with-text": "node fishUtils/script.find-files-with-text.js"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{scss}": ["stylelint --fix"]}, "jest": {"collectCoverageFrom": ["packages/*/src/**/*"], "coverageReporters": ["text", "cobertura"], "moduleFileExtensions": ["ts", "tsx", "js"], "moduleNameMapper": {"\\.(css|less|sass|scss)$": "<rootDir>/__mocks__/styleMock.js", "\\.(svg)$": "<rootDir>/__mocks__/svgMock.js", "\\.svg\\.js$": "<rootDir>/__mocks__/svgJsMock.js", "^amis\\-ui$": "<rootDir>/packages/amis-ui/src/index.tsx", "^amis\\-ui/lib/(.*)$": "<rootDir>/packages/amis-ui/src/$1", "^amis\\-core$": "<rootDir>/packages/amis-core/src/index.tsx", "^amis\\-formula$": "<rootDir>/packages/amis-formula/src/index.ts", "^office\\-viewer$": "<rootDir>/packages/office-viewer/src/index.ts", "^amis$": "<rootDir>/packages/amis/src/index.tsx"}, "setupFiles": ["jest-canvas-mock"], "setupFilesAfterEnv": ["<rootDir>/packages/amis-core/__tests__/jest.setup.js"], "snapshotFormat": {"escapeString": false, "printBasicPrototype": false}, "testEnvironment": "jsdom", "testPathIgnorePatterns": ["/node_modules/", "/.rollup.cache/"], "testRegex": "/.*\\.test\\.(ts|tsx|js)$", "transform": {"^.+\\.(t|j)sx?$": "@swc/jest"}, "verbose": true}, "dependencies": {"@ant-design/pro-chat": "^1.15.3", "antd-style": "^3.7.1", "fs-extra": "^11.3.0", "path-to-regexp": "^6.2.0", "postcss": "^8.4.14", "qs": "6.9.7", "smooth-signature": "^1.0.15", "tslib": "^2.8.1"}, "devDependencies": {"@babel/generator": "^7.22.9", "@babel/parser": "^7.22.7", "@babel/traverse": "^7.22.8", "@babel/types": "^7.22.5", "@eslint/js": "^9.18.0", "@fortawesome/fontawesome-free": "^6.1.1", "@rollup/plugin-replace": "^5.0.1", "@swc/core": "^1.3.107", "@swc/helpers": "^0.5.3", "@swc/jest": "^0.2.34", "@types/express": "^4.17.14", "@types/jest": "^28.1.0", "@types/js-yaml": "^4.0.5", "@types/marked": "^4.0.7", "@types/node": "^14.0.24", "@types/prismjs": "^1.26.0", "@types/react": "^18.0.24", "@types/react-dom": "^18.0.8", "@types/react-syntax-highlighter": "^15.5.11", "@vitejs/plugin-react": "^2.2.0", "amis-publish": "^1.0.5", "copy-to-clipboard": "3.3.1", "echarts": "5.4.0", "eslint": "^9.18.0", "express": "^4.18.2", "fis-optimizer-terser": "^1.0.1", "fis-parser-sass": "^1.2.0", "fis-parser-svgr": "^1.0.0", "fis3": "^3.5.0-beta.2", "fis3-deploy-skip-packed": "0.0.5", "fis3-hook-commonjs": "^0.1.31", "fis3-hook-node_modules": "^2.3.1", "fis3-hook-relative": "^2.0.3", "fis3-packager-deps-pack": "^0.1.2", "fis3-parser-typescript": "^1.4.0", "fis3-postpackager-loader": "^2.1.12", "fis3-prepackager-stand-alone-pack": "^1.0.0", "fis3-preprocessor-js-require-css": "^0.1.3", "fis3-preprocessor-js-require-file": "^0.1.3", "github-markdown-css": "^5.5.1", "husky": "^8.0.0", "jest": "^29.0.3", "jest-environment-jsdom": "^29.0.3", "js-yaml": "^4.1.0", "katex": "^0.16.9", "lerna": "^6.6.2", "lint-staged": "^12.1.2", "magic-string": "^0.26.7", "marked": "^4.2.1", "monaco-editor": "0.30.1", "plugin-react-i18n": "1.0.10", "postcss-scss": "^4.0.6", "prismjs": "^1.29.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-overlays": "5.1.1", "react-syntax-highlighter": "^15.5.0", "remark-gfm": "^4.0.0", "rollup": "^2.79.1", "rollup-plugin-external-globals": "^0.13.0", "rollup-pluginutils": "^2.8.2", "setprototypeof": "^1.2.0", "ts-jest": "^29.0.2", "typescript-eslint": "^8.19.1", "vite": "^4.3.9", "vite-plugin-external": "6.0.1", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-svgr": "^2.2.2", "zrender": "^5.3.2"}}